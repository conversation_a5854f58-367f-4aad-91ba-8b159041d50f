import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import json
from datetime import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple
import math
import random
from enum import Enum
from reportlab.lib.pagesizes import letter, landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors

@dataclass
class Animal:
    peso: float
    del_: float  # DEL em dias
    prod: float  # kg/dia
    gord: float  # %
    prot: float  # %
    lact: int    # número lactação
    gest: int    # dias gestação
    ecc: float   # escore corporal
    raca: str = "Holandesa"  # Para NRC 2021

@dataclass
class Ambiente:
    temp: float = 20  # °C
    umid: float = 60  # %
    vento: float = 2  # m/s
    pelo: str = "curto"  # curto/longo
    sombra: bool = True
    aspersao: bool = False
    ventilacao: bool = False

@dataclass
class Req:
    ms: float
    ndt: float
    pb: float
    pdr: float
    pndr: float
    ee: float
    fdn: float
    fda: float
    ca: float
    p: float
    enl: float
    em: float
    amido: float = 0
    pndf: float = 0  # FDN fisicamente efetiva
    chos: float = 0  # Carboidratos solúveis
    mg: float = 0
    k: float = 0
    na: float = 0
    cl: float = 0
    s: float = 0
    co: float = 0
    cu: float = 0
    fe: float = 0
    mn: float = 0
    zn: float = 0
    se: float = 0
    vit_a: float = 0
    vit_d: float = 0
    vit_e: float = 0

@dataclass
class Ingrediente:
    nome: str
    ms: float  # %
    pb: float  # % MS
    pdr: float  # % PB
    ee: float  # % MS
    fdn: float  # % MS
    fda: float  # % MS
    amido: float  # % MS
    enl: float  # Mcal/kg MS
    ca: float  # % MS
    p: float  # % MS
    preco: float = 0  # R$/kg
    min_inc: float = 0  # % mínima inclusão
    max_inc: float = 100  # % máxima inclusão
    tipo: str = "volumoso"  # volumoso/concentrado/mineral/aditivo

class BancoIngredientes:
    def __init__(self):
        self.ingredientes = {
            # Volumosos
            "Silagem Milho": Ingrediente("Silagem Milho", 32, 8.5, 65, 3.2, 45, 28, 28, 1.51, 0.25, 0.22, 0.35, 0, 60, "volumoso"),
            "Silagem Sorgo": Ingrediente("Silagem Sorgo", 30, 7.8, 63, 2.8, 52, 32, 22, 1.42, 0.28, 0.20, 0.32, 0, 55, "volumoso"),
            "Feno Tifton": Ingrediente("Feno Tifton", 88, 12, 60, 2.0, 72, 38, 3, 1.28, 0.45, 0.25, 0.80, 0, 40, "volumoso"),
            "Cana Picada": Ingrediente("Cana Picada", 28, 3.5, 55, 1.5, 55, 35, 2, 1.35, 0.20, 0.15, 0.25, 0, 45, "volumoso"),
            "Pasto Brachiaria": Ingrediente("Pasto Brachiaria", 22, 10, 62, 2.2, 68, 36, 4, 1.32, 0.35, 0.22, 0.15, 0, 50, "volumoso"),
            "Pre-secado Tifton": Ingrediente("Pre-secado Tifton", 45, 16, 58, 2.5, 65, 34, 5, 1.38, 0.50, 0.28, 0.65, 0, 35, "volumoso"),
            
            # Concentrados Energéticos
            "Milho Grão": Ingrediente("Milho Grão", 88, 9, 35, 4.0, 12, 3, 72, 2.00, 0.03, 0.28, 1.20, 0, 40, "concentrado"),
            "Sorgo Grão": Ingrediente("Sorgo Grão", 88, 10, 38, 3.5, 15, 5, 68, 1.92, 0.04, 0.30, 1.10, 0, 35, "concentrado"),
            "Polpa Cítrica": Ingrediente("Polpa Cítrica", 90, 7.5, 70, 2.5, 24, 18, 3, 1.85, 1.85, 0.12, 1.40, 0, 25, "concentrado"),
            "Casca Soja": Ingrediente("Casca Soja", 90, 11, 55, 2.0, 60, 42, 2, 1.77, 0.50, 0.18, 1.80, 0, 20, "concentrado"),
            "Mandioca Raspa": Ingrediente("Mandioca Raspa", 88, 3, 40, 0.8, 10, 5, 75, 1.95, 0.15, 0.10, 0.85, 0, 20, "concentrado"),
            "Melaço": Ingrediente("Melaço", 75, 5, 80, 0.2, 0, 0, 0, 1.70, 0.10, 0.08, 2.50, 0, 5, "concentrado"),
            
            # Concentrados Proteicos
            "Farelo Soja": Ingrediente("Farelo Soja", 88, 48, 65, 1.8, 14, 10, 2, 1.95, 0.30, 0.65, 3.50, 0, 30, "concentrado"),
            "Farelo Algodão": Ingrediente("Farelo Algodão", 90, 42, 55, 2.0, 28, 18, 2, 1.75, 0.20, 1.00, 2.80, 0, 20, "concentrado"),
            "Farelo Amendoim": Ingrediente("Farelo Amendoim", 90, 48, 60, 8.0, 15, 12, 3, 1.88, 0.18, 0.60, 3.20, 0, 15, "concentrado"),
            "Glúten Milho": Ingrediente("Glúten Milho", 90, 62, 35, 2.5, 12, 8, 15, 2.05, 0.05, 0.50, 4.80, 0, 10, "concentrado"),
            "DDG Milho": Ingrediente("DDG Milho", 88, 28, 45, 10, 35, 15, 8, 2.10, 0.08, 0.85, 2.60, 0, 20, "concentrado"),
            "Ureia": Ingrediente("Ureia", 99, 281, 100, 0, 0, 0, 0, 0, 0, 0, 8.00, 0, 1.5, "concentrado"),
            
            # Minerais e Aditivos
            "Calcário": Ingrediente("Calcário", 99, 0, 0, 0, 0, 0, 0, 0, 38, 0, 0.08, 0, 2, "mineral"),
            "Fosfato Bicálcico": Ingrediente("Fosfato Bicálcico", 99, 0, 0, 0, 0, 0, 0, 0, 24, 18.5, 0.35, 0, 2, "mineral"),
            "Sal Comum": Ingrediente("Sal Comum", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04, 0, 1, "mineral"),
            "Óxido Magnésio": Ingrediente("Óxido Magnésio", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.80, 0, 0.5, "mineral"),
            "Bicarbonato Sódio": Ingrediente("Bicarbonato Sódio", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.20, 0, 1.5, "aditivo"),
            "Gordura Protegida": Ingrediente("Gordura Protegida", 99, 0, 0, 85, 0, 0, 0, 5.80, 0, 0, 12.00, 0, 3, "concentrado"),
            
            # Subprodutos
            "Caroço Algodão": Ingrediente("Caroço Algodão", 90, 23, 40, 18, 44, 30, 2, 2.35, 0.15, 0.60, 1.60, 0, 15, "concentrado"),
            "Torta Palmiste": Ingrediente("Torta Palmiste", 92, 16, 50, 8, 60, 35, 1, 1.70, 0.25, 0.60, 1.50, 0, 10, "concentrado"),
            "DDGS": Ingrediente("DDGS", 88, 30, 48, 11, 32, 14, 10, 2.12, 0.10, 0.88, 2.70, 0, 20, "concentrado"),
            "Bagaço Cevada": Ingrediente("Bagaço Cevada", 22, 26, 55, 8, 45, 20, 5, 1.65, 0.30, 0.55, 0.60, 0, 15, "concentrado"),
        }
        
        self.custom_ingredientes = {}
    
    def get_all(self):
        return {**self.ingredientes, **self.custom_ingredientes}
    
    def add_custom(self, ing: Ingrediente):
        self.custom_ingredientes[ing.nome] = ing

class NutriCalc:
    def __init__(self):
        self.models = {
            'NRC 2021': self.nrc2021,
            'NRC 2001': self.nrc2001,
            'CNCPS 6.5': self.cncps,
            'BR-CORTE 2016': self.brcorte,
            'INRA 2018': self.inra,
            'AFRC 1993': self.afrc
        }
    
    def calc_thi(self, temp: float, umid: float) -> float:
        """Índice de Temperatura-Umidade"""
        return (1.8 * temp + 32) - (0.55 - 0.0055 * umid) * (1.8 * temp - 26)
    
    def nrc2021(self, a: Animal, amb: Optional[Ambiente] = None) -> Req:
        """NRC 2021 com ajustes ambientais"""
        if not amb:
            amb = Ambiente()
        
        # THI e estresse térmico
        thi = self.calc_thi(amb.temp, amb.umid)
        
        # Ajuste raça
        raca_mult = {
            "Holandesa": 1.0,
            "Jersey": 0.85,
            "Gir": 0.90,
            "Girolando": 0.92,
            "Pardo Suíço": 0.95
        }
        rm = raca_mult.get(a.raca, 1.0)
        
        # Manutenção base
        em_mant = 0.08 * (a.peso ** 0.75) * rm
        
        # Ajuste por estresse térmico
        if thi > 72:
            stress_factor = 1 + 0.005 * (thi - 72)
            em_mant *= stress_factor
            
            # Redução consumo
            ms_reduction = max(0.8, 1 - 0.005 * (thi - 72))
        else:
            ms_reduction = 1.0
        
        # Ajuste por sistema de resfriamento
        if amb.sombra:
            em_mant *= 0.97
        if amb.aspersao:
            em_mant *= 0.95
        if amb.ventilacao:
            em_mant *= 0.96
        
        # Produção com correção
        enl_leite = (0.0929 * a.gord + 0.0563 * a.prot + 0.0395 * 4.85 + 0.192) * a.prod
        
        # Gestação
        em_gest = 0 if a.gest < 190 else 0.00318 * a.gest - 0.0352
        
        # Ganho/perda peso adaptativo
        if a.del_ < 100:
            target_ecc = 3.0
            delta_peso = (target_ecc - a.ecc) * 0.5
        elif a.del_ < 200:
            target_ecc = 3.25
            delta_peso = (target_ecc - a.ecc) * 0.3
        else:
            target_ecc = 3.5
            delta_peso = (target_ecc - a.ecc) * 0.2
        
        em_ganho = abs(delta_peso) * 5.12
        
        em_total = em_mant + enl_leite/0.65 + em_gest + em_ganho
        
        # MS com ajuste ambiental
        ms_base = 0.0185 * a.peso + 0.305 * a.prod
        ms = ms_base * ms_reduction
        
        # Proteína metabolizável
        pm_mant = 3.8 * (a.peso ** 0.75) * rm
        pm_leite = a.prod * a.prot * 10 / 0.67
        pm_gest = 0 if a.gest < 190 else (0.69 * a.gest - 69.2)
        
        # Ajuste proteína por estresse
        if thi > 72:
            pm_stress = pm_mant * 0.1
        else:
            pm_stress = 0
        
        pm_total = pm_mant + pm_leite + pm_gest + pm_stress
        
        pb = pm_total / 0.65
        pdr = pb * 0.63
        pndr = pb * 0.37
        
        # Minerais expandidos
        ca = 1.23 * a.prod + 15.4
        p = 0.99 * a.prod + 11.0
        mg = 0.20 * a.prod + 10
        k = 1.5 * a.prod + 35
        na = 0.63 * a.prod + 15
        cl = 0.90 * a.prod + 20
        s = 0.20 * ms
        
        # Microminerais (mg/dia)
        co = 0.11 * ms
        cu = 15 * ms
        fe = 50 * ms
        mn = 40 * ms
        zn = 60 * ms
        se = 0.3 * ms
        
        # Vitaminas (UI/dia)
        vit_a = 110 * a.peso
        vit_d = 30 * a.peso
        vit_e = 1.6 * a.peso
        
        # Fibra ajustada
        fdn_min = ms * 0.28 if a.prod > 35 else ms * 0.33
        fdn = fdn_min * (1 + 0.02 * max(0, thi - 72))  # Mais fibra em calor
        pndf = fdn * 0.75  # FDN fisicamente efetiva
        fda = fdn * 0.55
        
        # Amido recomendado
        amido = ms * (0.25 - 0.002 * max(0, thi - 72))
        
        # Carboidratos solúveis
        chos = ms * 0.06
        
        return Req(
            ms=ms, ndt=em_total/0.04409, pb=pb/1000,
            pdr=pdr/1000, pndr=pndr/1000, ee=ms*0.045,
            fdn=fdn, fda=fda, ca=ca, p=p,
            enl=enl_leite, em=em_total,
            amido=amido, pndf=pndf, chos=chos,
            mg=mg, k=k, na=na, cl=cl, s=s,
            co=co, cu=cu, fe=fe, mn=mn, zn=zn, se=se,
            vit_a=vit_a, vit_d=vit_d, vit_e=vit_e
        )
    
    def nrc2001(self, a: Animal, amb: Optional[Ambiente] = None) -> Req:
        em_mant = 0.08 * (a.peso ** 0.75)
        enl_leite = (0.0929 * a.gord + 0.0547 * a.prot + 0.192) * a.prod
        em_gest = 0 if a.gest < 190 else 0.00318 * a.gest - 0.0352
        
        delta_peso = 0
        if a.del_ < 100:
            delta_peso = -0.5 if a.ecc < 3 else 0
        elif a.del_ > 200:
            delta_peso = 0.3 if a.ecc < 3.5 else 0
        
        em_total = em_mant + enl_leite/0.64 + em_gest + abs(delta_peso) * 4.92
        ms = 0.0185 * a.peso + 0.305 * a.prod
        
        pm_mant = 3.8 * (a.peso ** 0.75)
        pm_leite = a.prod * a.prot * 10 / 0.67
        pm_gest = 0 if a.gest < 190 else (0.69 * a.gest - 69.2)
        pm_total = pm_mant + pm_leite + pm_gest
        
        pb = pm_total / 0.67
        pdr = pb * 0.65
        pndr = pb * 0.35
        
        ca = 1.23 * a.prod + 15.4
        p = 0.99 * a.prod + 11.0
        
        fdn = ms * 0.28 if a.prod > 35 else ms * 0.33
        fda = fdn * 0.55
        
        amido = ms * 0.24
        pndf = fdn * 0.72
        
        return Req(
            ms=ms, ndt=em_total/0.04409, pb=pb/1000,
            pdr=pdr/1000, pndr=pndr/1000, ee=ms*0.04,
            fdn=fdn, fda=fda, ca=ca, p=p,
            enl=enl_leite, em=em_total,
            amido=amido, pndf=pndf
        )
    
    def cncps(self, a: Animal, amb: Optional[Ambiente] = None) -> Req:
        em_mant = 0.086 * (a.peso ** 0.75)
        ativ = 1.1 if a.del_ < 200 else 1.05
        em_mant *= ativ
        
        enl_leite = (0.0929 * a.gord + 0.0563 * a.prot + 0.192) * a.prod
        em_gest = 0 if a.gest < 190 else 0.00318 * a.gest - 0.0352
        
        em_total = em_mant + enl_leite/0.65 + em_gest
        ms = 0.019 * a.peso + 0.31 * a.prod
        
        pm_mant = 4.1 * (a.peso ** 0.75)
        pm_leite = a.prod * a.prot * 10 / 0.65
        pm_gest = 0 if a.gest < 190 else (0.69 * a.gest - 69.2)
        pm_total = pm_mant + pm_leite + pm_gest
        
        pb = pm_total / 0.64
        pdr = pb * 0.63
        pndr = pb * 0.37
        
        ca = 1.27 * a.prod + 16.2
        p = 1.02 * a.prod + 11.5
        
        fdn = ms * (0.31 - 0.002 * a.prod) if a.prod < 40 else ms * 0.23
        fda = fdn * 0.53
        
        amido = ms * 0.23
        pndf = fdn * 0.70
        
        return Req(
            ms=ms, ndt=em_total/0.04409, pb=pb/1000,
            pdr=pdr/1000, pndr=pndr/1000, ee=ms*0.045,
            fdn=fdn, fda=fda, ca=ca, p=p,
            enl=enl_leite, em=em_total,
            amido=amido, pndf=pndf
        )
    
    def brcorte(self, a: Animal, amb: Optional[Ambiente] = None) -> Req:
        em_mant = 0.077 * (a.peso ** 0.75)
        em_mant *= 1.2  # Correção tropical
        
        enl_leite = (0.092 * a.gord + 0.055 * a.prot + 0.19) * a.prod
        em_gest = 0 if a.gest < 190 else 0.0032 * a.gest - 0.035
        
        em_total = em_mant + enl_leite/0.62 + em_gest
        ms = 0.021 * a.peso + 0.32 * a.prod
        
        pm_mant = 3.6 * (a.peso ** 0.75)
        pm_leite = a.prod * a.prot * 10 / 0.64
        pm_gest = 0 if a.gest < 190 else (0.7 * a.gest - 70)
        pm_total = pm_mant + pm_leite + pm_gest
        
        pb = pm_total / 0.62
        pdr = pb * 0.68
        pndr = pb * 0.32
        
        ca = 1.25 * a.prod + 15.8
        p = 1.01 * a.prod + 11.3
        
        fdn = ms * 0.35 if a.prod < 30 else ms * 0.30
        fda = fdn * 0.58
        
        amido = ms * 0.20  # Menos amido em dietas tropicais
        pndf = fdn * 0.68
        
        return Req(
            ms=ms, ndt=em_total/0.04409, pb=pb/1000,
            pdr=pdr/1000, pndr=pndr/1000, ee=ms*0.035,
            fdn=fdn, fda=fda, ca=ca, p=p,
            enl=enl_leite, em=em_total,
            amido=amido, pndf=pndf
        )
    
    def inra(self, a: Animal, amb: Optional[Ambiente] = None) -> Req:
        em_mant = 0.083 * (a.peso ** 0.75)
        enl_leite = (0.093 * a.gord + 0.056 * a.prot + 0.195) * a.prod
        em_gest = 0 if a.gest < 180 else 0.0033 * a.gest - 0.036
        
        em_total = em_mant + enl_leite/0.63 + em_gest
        ms = 0.018 * a.peso + 0.30 * a.prod
        
        pm_mant = 3.9 * (a.peso ** 0.75)
        pm_leite = a.prod * a.prot * 10 / 0.66
        pm_gest = 0 if a.gest < 180 else (0.71 * a.gest - 71)
        pm_total = pm_mant + pm_leite + pm_gest
        
        pb = pm_total / 0.65
        pdr = pb * 0.64
        pndr = pb * 0.36
        
        ca = 1.24 * a.prod + 15.6
        p = 1.0 * a.prod + 11.2
        
        fdn = ms * 0.32 if a.prod < 35 else ms * 0.27
        fda = fdn * 0.54
        
        amido = ms * 0.22
        pndf = fdn * 0.71
        
        return Req(
            ms=ms, ndt=em_total/0.04409, pb=pb/1000,
            pdr=pdr/1000, pndr=pndr/1000, ee=ms*0.042,
            fdn=fdn, fda=fda, ca=ca, p=p,
            enl=enl_leite, em=em_total,
            amido=amido, pndf=pndf
        )
    
    def afrc(self, a: Animal, amb: Optional[Ambiente] = None) -> Req:
        em_mant = 0.082 * (a.peso ** 0.75)
        enl_leite = (0.091 * a.gord + 0.054 * a.prot + 0.193) * a.prod
        em_gest = 0 if a.gest < 185 else 0.0031 * a.gest - 0.034
        
        em_total = em_mant + enl_leite/0.64 + em_gest
        ms = 0.020 * a.peso + 0.31 * a.prod
        
        pm_mant = 3.7 * (a.peso ** 0.75)
        pm_leite = a.prod * a.prot * 10 / 0.68
        pm_gest = 0 if a.gest < 185 else (0.68 * a.gest - 68)
        pm_total = pm_mant + pm_leite + pm_gest
        
        pb = pm_total / 0.66
        pdr = pb * 0.66
        pndr = pb * 0.34
        
        ca = 1.26 * a.prod + 15.9
        p = 1.01 * a.prod + 11.4
        
        fdn = ms * 0.33 if a.prod < 32 else ms * 0.28
        fda = fdn * 0.56
        
        amido = ms * 0.23
        pndf = fdn * 0.73
        
        return Req(
            ms=ms, ndt=em_total/0.04409, pb=pb/1000,
            pdr=pdr/1000, pndr=pndr/1000, ee=ms*0.041,
            fdn=fdn, fda=fda, ca=ca, p=p,
            enl=enl_leite, em=em_total,
            amido=amido, pndf=pndf
        )

class DietFormulator:
    def __init__(self, req: Req, ingredientes: Dict[str, Ingrediente]):
        self.req = req
        self.ingredientes = ingredientes
        
    def calculate_nutrients(self, diet: Dict[str, float]) -> Dict[str, float]:
        """Calcular nutrientes totais da dieta com todos os parâmetros"""
        nut = {
            "ms": 0, "pb": 0, "pdr": 0, "pndr": 0,
            "ee": 0, "fdn": 0, "fda": 0, "amido": 0,
            "enl": 0, "ca": 0, "p": 0, "custo": 0,
            "mg": 0, "k": 0, "na": 0, "cl": 0, "s": 0,
            "co": 0, "cu": 0, "fe": 0, "mn": 0, "zn": 0, "se": 0,
            "vit_a": 0, "vit_d": 0, "vit_e": 0
        }
        
        for ing_name, qtd_mn in diet.items():
            if ing_name in self.ingredientes:
                ing = self.ingredientes[ing_name]
                qtd_ms = qtd_mn * (ing.ms / 100)  # Converter MN para MS
                
                nut["ms"] += qtd_ms
                nut["pb"] += qtd_ms * ing.pb / 100
                nut["pdr"] += qtd_ms * ing.pb * ing.pdr / 10000
                nut["pndr"] += qtd_ms * ing.pb * (100-ing.pdr) / 10000
                nut["ee"] += qtd_ms * ing.ee / 100
                nut["fdn"] += qtd_ms * ing.fdn / 100
                nut["fda"] += qtd_ms * ing.fda / 100
                nut["amido"] += qtd_ms * ing.amido / 100
                nut["enl"] += qtd_ms * ing.enl
                nut["ca"] += qtd_ms * ing.ca / 100
                nut["p"] += qtd_ms * ing.p / 100
                nut["custo"] += qtd_mn * ing.preco
                
                # Minerais e vitaminas
                nut["mg"] += qtd_ms * 0.2 / 100  # Estimativa
                nut["k"] += qtd_ms * 1.5 / 100   # Estimativa
                nut["na"] += qtd_ms * 0.15 / 100  # Estimativa
                nut["cl"] += qtd_ms * 0.2 / 100   # Estimativa
                nut["s"] += qtd_ms * 0.2 / 100    # Estimativa
                
                # Microminerais (mg)
                nut["co"] += qtd_ms * 0.1
                nut["cu"] += qtd_ms * 10
                nut["fe"] += qtd_ms * 30
                nut["mn"] += qtd_ms * 20
                nut["zn"] += qtd_ms * 30
                nut["se"] += qtd_ms * 0.1
                
                # Vitaminas
                nut["vit_a"] += qtd_ms * 100
                nut["vit_d"] += qtd_ms * 30
                nut["vit_e"] += qtd_ms * 2
        
        # Calcular NDT estimado
        nut["ndt"] = nut["enl"] / 0.0245
        
        return nut

class App:
    def __init__(self, root):
        self.root = root
        self.root.title("NutriLeite Pro 2.0 - Sistema Avançado de Nutrição Bovina")
        self.root.geometry("1400x800")
        
        self.calc = NutriCalc()
        self.banco = BancoIngredientes()
        self.results = {}
        self.diets = {}
        self.manual_diet = {}  # Dieta manual (em MN)
        self.ambiente = Ambiente()
        
        # Estilo
        style = ttk.Style()
        style.theme_use('clam')
        
        self.setup_ui()
        self.refresh_ingredients()  # Carregar ingredientes iniciais
    
    def setup_ui(self):
        # Notebook para abas
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Aba 1: Dados e Cálculo
        self.tab1 = ttk.Frame(self.notebook)
        self.notebook.add(self.tab1, text="📊 Cálculo de Requisitos")
        self.setup_tab1()
        
        # Aba 2: Ambiente (NRC 2021)
        self.tab2 = ttk.Frame(self.notebook)
        self.notebook.add(self.tab2, text="🌡️ Ambiente (NRC 2021)")
        self.setup_tab2()
        
        # Aba 3: Formulação de Dietas
        self.tab3 = ttk.Frame(self.notebook)
        self.notebook.add(self.tab3, text="🥗 Formulação de Dietas")
        self.setup_tab3()
        
        # Aba 4: Ingredientes
        self.tab4 = ttk.Frame(self.notebook)
        self.notebook.add(self.tab4, text="🌾 Banco de Ingredientes")
        self.setup_tab4()
        
        # Aba 5: Análises e Comparações
        self.tab5 = ttk.Frame(self.notebook)
        self.notebook.add(self.tab5, text="📈 Análises Avançadas")
        self.setup_tab5()
    
    def setup_tab1(self):
        # Frame principal
        main = ttk.Frame(self.tab1, padding="10")
        main.pack(fill=tk.BOTH, expand=True)
        
        # Frame superior para entrada e modelos
        top_frame = ttk.Frame(main)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Entrada de dados
        input_frame = ttk.LabelFrame(top_frame, text="Dados do Animal", padding="10")
        input_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        labels = [
            ("Peso vivo (kg):", "peso"),
            ("DEL (dias):", "del"),
            ("Produção (kg/dia):", "prod"),
            ("Gordura (%):", "gord"),
            ("Proteína (%):", "prot"),
            ("Nº Lactação:", "lact"),
            ("Gestação (dias):", "gest"),
            ("ECC (1-5):", "ecc")
        ]
        
        self.entries = {}
        for i, (label, key) in enumerate(labels):
            row = i // 2
            col = (i % 2) * 2
            ttk.Label(input_frame, text=label).grid(row=row, column=col, sticky=tk.W, pady=2)
            self.entries[key] = ttk.Entry(input_frame, width=12)
            self.entries[key].grid(row=row, column=col+1, pady=2, padx=(0, 10))
        
        # Raça
        ttk.Label(input_frame, text="Raça:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.raca_var = tk.StringVar(value="Holandesa")
        raca_combo = ttk.Combobox(input_frame, textvariable=self.raca_var, width=10)
        raca_combo['values'] = ["Holandesa", "Jersey", "Gir", "Girolando", "Pardo Suíço"]
        raca_combo.grid(row=4, column=1, pady=2)
        
        # Valores padrão
        defaults = {"peso": "550", "del": "120", "prod": "25", "gord": "3.8",
                   "prot": "3.2", "lact": "2", "gest": "0", "ecc": "3.0"}
        for k, v in defaults.items():
            self.entries[k].insert(0, v)
        
        # Seleção de modelos
        model_frame = ttk.LabelFrame(top_frame, text="Modelos Nutricionais", padding="10")
        model_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.model_vars = {}
        model_info = {
            'NRC 2021': ("🆕 Modelo mais recente com ajustes ambientais", True),
            'NRC 2001': ("📚 Padrão USA tradicional", True),
            'CNCPS 6.5': ("🔬 Cornell - Mais detalhado", False),
            'BR-CORTE 2016': ("🇧🇷 Adaptado Brasil/Tropical", True),
            'INRA 2018': ("🇫🇷 Sistema Francês", False),
            'AFRC 1993': ("🇬🇧 Sistema Reino Unido", False)
        }
        
        for i, (model, (info, default)) in enumerate(model_info.items()):
            var = tk.BooleanVar(value=default)
            self.model_vars[model] = var
            cb = ttk.Checkbutton(model_frame, text=model, variable=var)
            cb.grid(row=i, column=0, sticky=tk.W, pady=2)
            ttk.Label(model_frame, text=info, font=('Arial', 8), 
                     foreground='gray').grid(row=i, column=1, sticky=tk.W, padx=10)
        
        # Botões
        btn_frame = ttk.Frame(main)
        btn_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(btn_frame, text="🔄 Calcular", command=self.calculate).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="📊 Comparar", command=self.compare).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="💾 Exportar PDF", command=self.export_pdf).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🗑️ Limpar", command=self.clear).pack(side=tk.LEFT, padx=5)
        
        # Resultados
        result_frame = ttk.LabelFrame(main, text="Resultados", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, width=130, height=25, 
                                                     font=('Courier', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
    
    def setup_tab2(self):
        """Configuração ambiental para NRC 2021"""
        main = ttk.Frame(self.tab2, padding="20")
        main.pack(fill=tk.BOTH, expand=True)
        
        # Condições ambientais
        env_frame = ttk.LabelFrame(main, text="Condições Ambientais", padding="15")
        env_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Temperatura e Umidade
        ttk.Label(env_frame, text="Temperatura (°C):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.temp_var = tk.DoubleVar(value=20)
        temp_scale = ttk.Scale(env_frame, from_=0, to=45, variable=self.temp_var, 
                               orient=tk.HORIZONTAL, length=200)
        temp_scale.grid(row=0, column=1, pady=5)
        self.temp_label = ttk.Label(env_frame, text="20°C")
        self.temp_label.grid(row=0, column=2, padx=10)
        temp_scale.config(command=lambda v: self.temp_label.config(text=f"{float(v):.1f}°C"))
        
        ttk.Label(env_frame, text="Umidade (%):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.umid_var = tk.DoubleVar(value=60)
        umid_scale = ttk.Scale(env_frame, from_=20, to=100, variable=self.umid_var,
                               orient=tk.HORIZONTAL, length=200)
        umid_scale.grid(row=1, column=1, pady=5)
        self.umid_label = ttk.Label(env_frame, text="60%")
        self.umid_label.grid(row=1, column=2, padx=10)
        umid_scale.config(command=lambda v: self.umid_label.config(text=f"{float(v):.0f}%"))
        
        ttk.Label(env_frame, text="Vento (m/s):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.vento_var = tk.DoubleVar(value=2)
        vento_scale = ttk.Scale(env_frame, from_=0, to=10, variable=self.vento_var,
                                orient=tk.HORIZONTAL, length=200)
        vento_scale.grid(row=2, column=1, pady=5)
        self.vento_label = ttk.Label(env_frame, text="2.0 m/s")
        self.vento_label.grid(row=2, column=2, padx=10)
        vento_scale.config(command=lambda v: self.vento_label.config(text=f"{float(v):.1f} m/s"))
        
        # THI Display
        self.thi_frame = ttk.LabelFrame(env_frame, text="Índice THI", padding="10")
        self.thi_frame.grid(row=0, column=3, rowspan=3, padx=20, sticky=(tk.N, tk.S))
        
        self.thi_value = ttk.Label(self.thi_frame, text="68", font=('Arial', 24, 'bold'))
        self.thi_value.pack()
        self.thi_status = ttk.Label(self.thi_frame, text="Conforto", font=('Arial', 12))
        self.thi_status.pack()
        
        # Atualizar THI
        self.update_thi()
        temp_scale.config(command=lambda v: (self.temp_label.config(text=f"{float(v):.1f}°C"), 
                                            self.update_thi()))
        umid_scale.config(command=lambda v: (self.umid_label.config(text=f"{float(v):.0f}%"),
                                            self.update_thi()))
        
        # Manejo
        manejo_frame = ttk.LabelFrame(main, text="Sistema de Resfriamento", padding="15")
        manejo_frame.pack(fill=tk.X, pady=10)
        
        self.sombra_var = tk.BooleanVar(value=True)
        self.aspersao_var = tk.BooleanVar(value=False)
        self.ventilacao_var = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(manejo_frame, text="Sombra disponível", 
                       variable=self.sombra_var).grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Checkbutton(manejo_frame, text="Sistema de aspersão", 
                       variable=self.aspersao_var).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Checkbutton(manejo_frame, text="Ventilação forçada", 
                       variable=self.ventilacao_var).grid(row=0, column=2, sticky=tk.W, pady=5)
        
        # Tipo de pelo
        ttk.Label(manejo_frame, text="Tipo de pelo:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.pelo_var = tk.StringVar(value="curto")
        pelo_combo = ttk.Combobox(manejo_frame, textvariable=self.pelo_var, width=10)
        pelo_combo['values'] = ["curto", "longo"]
        pelo_combo.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # Info sobre estresse térmico
        info_frame = ttk.LabelFrame(main, text="Informações sobre Estresse Térmico", padding="15")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        info_text = """
        ÍNDICE THI (Temperature-Humidity Index):
        
        < 68: Conforto térmico
        68-72: Estresse leve
        72-78: Estresse moderado  
        78-82: Estresse severo
        > 82: Estresse muito severo
        
        IMPACTOS DO ESTRESSE TÉRMICO:
        • Redução no consumo de MS
        • Aumento nas necessidades de mantença
        • Redução na produção de leite
        • Alteração na composição do leite
        • Problemas reprodutivos
        
        MEDIDAS MITIGADORAS:
        • Sombra: reduz 3% das exigências
        • Aspersão: reduz 5% das exigências  
        • Ventilação: reduz 4% das exigências
        """
        
        text_widget = tk.Text(info_frame, height=15, width=50, font=('Arial', 9))
        text_widget.insert(1.0, info_text)
        text_widget.config(state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True)
    
    def setup_tab3(self):
        """Formulação de dietas - COMPLETAMENTE REPROJETADA"""
        main = ttk.Frame(self.tab3, padding="10")
        main.pack(fill=tk.BOTH, expand=True)
        
        # Frame de controle
        control_frame = ttk.LabelFrame(main, text="Controles de Formulação", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(control_frame, text="Modelo Base:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.diet_model_var = tk.StringVar(value="NRC 2021")
        model_combo = ttk.Combobox(control_frame, textvariable=self.diet_model_var, width=15)
        model_combo['values'] = list(self.calc.models.keys())
        model_combo.grid(row=0, column=1, pady=5, padx=5)
        
        ttk.Button(control_frame, text="📋 Limpar Dieta", 
                  command=self.clear_manual_diet).grid(row=0, column=2, padx=20)
        
        # Frame para adição manual de ingredientes
        manual_frame = ttk.LabelFrame(main, text="Adicionar Ingredientes Manualmente", padding="10")
        manual_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Seleção de ingrediente
        ttk.Label(manual_frame, text="Ingrediente:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.ing_selector = ttk.Combobox(manual_frame, width=30)
        self.ing_selector.grid(row=0, column=1, pady=5, padx=5)
        self.update_ingredient_selector()
        
        # Quantidade em Matéria Natural
        ttk.Label(manual_frame, text="MN (kg/dia):").grid(row=0, column=2, sticky=tk.W, pady=5, padx=(20,0))
        self.qtd_entry = ttk.Entry(manual_frame, width=10)
        self.qtd_entry.grid(row=0, column=3, pady=5, padx=5)
        
        # Botões
        ttk.Button(manual_frame, text="➕ Adicionar", 
                  command=self.add_manual_ingredient).grid(row=0, column=4, padx=10)
        ttk.Button(manual_frame, text="🗑️ Remover Selecionado", 
                  command=self.remove_selected_ingredient).grid(row=0, column=5, padx=5)
        
        # Frame de resultados da dieta
        diet_frame = ttk.LabelFrame(main, text="Dieta Formulada", padding="10")
        diet_frame.pack(fill=tk.BOTH, expand=True)
        
        # Criar Treeview para mostrar dieta
        columns = ('Ingrediente', 'MN kg/dia', 'MS kg/dia', '% MS', 'R$/dia')
        self.diet_tree = ttk.Treeview(diet_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.diet_tree.heading(col, text=col)
            self.diet_tree.column(col, width=120)
        
        self.diet_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(diet_frame, orient=tk.VERTICAL, command=self.diet_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.diet_tree.configure(yscrollcommand=scrollbar.set)
        
        # Frame de análise nutricional
        analysis_frame = ttk.LabelFrame(main, text="Análise Nutricional da Dieta", padding="10")
        analysis_frame.pack(fill=tk.X, pady=10)
        
        self.diet_analysis = scrolledtext.ScrolledText(analysis_frame, width=130, height=15,
                                                       font=('Courier', 9))
        self.diet_analysis.pack(fill=tk.BOTH, expand=True)
        
        # Botão para calcular análise
        ttk.Button(analysis_frame, text="🔄 Calcular Análise Completa", 
                  command=self.calculate_manual_analysis).pack(pady=5)
    
    def setup_tab4(self):
        """Banco de ingredientes"""
        main = ttk.Frame(self.tab4, padding="10")
        main.pack(fill=tk.BOTH, expand=True)
        
        # Frame de lista
        list_frame = ttk.LabelFrame(main, text="Ingredientes Disponíveis", padding="10")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Treeview para ingredientes
        columns = ('Nome', 'MS%', 'PB%', 'FDN%', 'Amido%', 'ENl', 'R$/kg', 'Tipo')
        self.ing_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.ing_tree.heading(col, text=col)
            self.ing_tree.column(col, width=80 if col != 'Nome' else 150)
        
        self.ing_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.ing_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.ing_tree.configure(yscrollcommand=scrollbar.set)
        
        # Frame de adição
        add_frame = ttk.LabelFrame(main, text="Adicionar Ingrediente", padding="10")
        add_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        # Campos para novo ingrediente
        fields = [
            ("Nome:", "nome"),
            ("MS (%):", "ms"),
            ("PB (% MS):", "pb"),
            ("PDR (% PB):", "pdr"),
            ("EE (% MS):", "ee"),
            ("FDN (% MS):", "fdn"),
            ("FDA (% MS):", "fda"),
            ("Amido (% MS):", "amido"),
            ("ENl (Mcal/kg):", "enl"),
            ("Ca (% MS):", "ca"),
            ("P (% MS):", "p"),
            ("Preço (R$/kg):", "preco")
        ]
        
        self.ing_entries = {}
        for i, (label, key) in enumerate(fields):
            ttk.Label(add_frame, text=label).grid(row=i, column=0, sticky=tk.W, pady=2)
            self.ing_entries[key] = ttk.Entry(add_frame, width=10)
            self.ing_entries[key].grid(row=i, column=1, pady=2)
        
        # Tipo
        ttk.Label(add_frame, text="Tipo:").grid(row=len(fields), column=0, sticky=tk.W, pady=2)
        self.ing_tipo_var = tk.StringVar(value="volumoso")
        tipo_combo = ttk.Combobox(add_frame, textvariable=self.ing_tipo_var, width=12)
        tipo_combo['values'] = ["volumoso", "concentrado", "mineral", "aditivo"]
        tipo_combo.grid(row=len(fields), column=1, pady=2)
        
        # Botões
        ttk.Button(add_frame, text="➕ Adicionar", 
                  command=self.add_ingredient).grid(row=len(fields)+1, column=0, columnspan=2, pady=10)
        ttk.Button(add_frame, text="🗑️ Remover Selecionado",
                  command=self.remove_ingredient).grid(row=len(fields)+2, column=0, columnspan=2, pady=5)
        
    def setup_tab5(self):
        """Análises avançadas - Simplificada"""
        main = ttk.Frame(self.tab5, padding="10")
        main.pack(fill=tk.BOTH, expand=True)
        
        # Frame de controles
        control_frame = ttk.LabelFrame(main, text="Análises Disponíveis", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(control_frame, text="📈 Projeção de Produção",
                  command=self.production_projection).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="💰 Análise Econômica",
                  command=self.economic_analysis).pack(side=tk.LEFT, padx=5)
        
        # Frame de resultados
        result_frame = ttk.LabelFrame(main, text="Resultados da Análise", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.analysis_text = scrolledtext.ScrolledText(result_frame, width=130, height=30,
                                                       font=('Courier', 9))
        self.analysis_text.pack(fill=tk.BOTH, expand=True)
    
    def update_thi(self):
        """Atualizar cálculo de THI"""
        temp = self.temp_var.get()
        umid = self.umid_var.get()
        thi = self.calc.calc_thi(temp, umid)
        
        self.thi_value.config(text=f"{thi:.1f}")
        
        if thi < 68:
            status = "Conforto"
            color = "green"
        elif thi < 72:
            status = "Estresse leve"
            color = "yellow"
        elif thi < 78:
            status = "Estresse moderado"
            color = "orange"
        elif thi < 82:
            status = "Estresse severo"
            color = "red"
        else:
            status = "Estresse muito severo"
            color = "darkred"
        
        self.thi_status.config(text=status, foreground=color)
        self.thi_value.config(foreground=color)
    
    def get_animal(self) -> Animal:
        try:
            return Animal(
                peso=float(self.entries['peso'].get()),
                del_=float(self.entries['del'].get()),
                prod=float(self.entries['prod'].get()),
                gord=float(self.entries['gord'].get()),
                prot=float(self.entries['prot'].get()),
                lact=int(self.entries['lact'].get()),
                gest=int(self.entries['gest'].get()),
                ecc=float(self.entries['ecc'].get()),
                raca=self.raca_var.get()
            )
        except ValueError:
            messagebox.showerror("Erro", "Verifique os valores inseridos")
            return None
    
    def get_ambiente(self) -> Ambiente:
        return Ambiente(
            temp=self.temp_var.get(),
            umid=self.umid_var.get(),
            vento=self.vento_var.get(),
            pelo=self.pelo_var.get(),
            sombra=self.sombra_var.get(),
            aspersao=self.aspersao_var.get(),
            ventilacao=self.ventilacao_var.get()
        )
    
    def calculate(self):
        animal = self.get_animal()
        if not animal:
            return
        
        ambiente = self.get_ambiente()
        
        self.results = {}
        selected = [m for m, v in self.model_vars.items() if v.get()]
        
        if not selected:
            messagebox.showwarning("Aviso", "Selecione pelo menos um modelo")
            return
        
        for model in selected:
            if model == "NRC 2021":
                self.results[model] = self.calc.models[model](animal, ambiente)
            else:
                self.results[model] = self.calc.models[model](animal)
        
        self.show_results()
    
    def show_results(self):
        self.result_text.delete(1.0, tk.END)
        
        if not self.results:
            return
        
        header = f"{'='*120}\n"
        header += f"{'REQUISITOS NUTRICIONAIS CALCULADOS':^120}\n"
        header += f"{'='*120}\n\n"
        self.result_text.insert(tk.END, header)
        
        for model, req in self.results.items():
            text = f"╔{'═'*60}╗\n"
            text += f"║ {model:^58} ║\n"
            text += f"╚{'═'*60}╝\n\n"
            
            # Dados principais
            text += "NUTRIENTES PRINCIPAIS:\n"
            text += f"┌{'─'*30}┬{'─'*15}┬{'─'*15}┬{'─'*20}┐\n"
            text += f"│ {'Nutriente':<28} │ {'Quantidade':<13} │ {'% MS':^13} │ {'Unidade':^18} │\n"
            text += f"├{'─'*30}┼{'─'*15}┼{'─'*15}┼{'─'*20}┤\n"
            
            ms_total = req.ms
            data = [
                ("Matéria Seca", req.ms, 100, "kg/dia"),
                ("NDT", req.ndt, (req.ndt/ms_total*100) if ms_total else 0, "kg/dia"),
                ("Proteína Bruta", req.pb, (req.pb/ms_total*100) if ms_total else 0, "kg/dia"),
                ("PDR", req.pdr, (req.pdr/ms_total*100) if ms_total else 0, "kg/dia"),
                ("PNDR", req.pndr, (req.pndr/ms_total*100) if ms_total else 0, "kg/dia"),
                ("Extrato Etéreo", req.ee, (req.ee/ms_total*100) if ms_total else 0, "kg/dia"),
                ("FDN", req.fdn, (req.fdn/ms_total*100) if ms_total else 0, "kg/dia"),
                ("FDA", req.fda, (req.fda/ms_total*100) if ms_total else 0, "kg/dia"),
                ("Amido", req.amido, (req.amido/ms_total*100) if ms_total else 0, "kg/dia"),
                ("FDN efetiva", req.pndf, (req.pndf/ms_total*100) if ms_total else 0, "kg/dia"),
            ]
            
            for nut, val, perc, unit in data:
                text += f"│ {nut:<28} │ {val:>13.2f} │ {perc:>13.1f} │ {unit:^18} │\n"
            
            text += f"├{'─'*30}┼{'─'*15}┼{'─'*15}┼{'─'*20}┤\n"
            
            # Energia
            text += f"│ {'ENl (Produção)':<28} │ {req.enl:>13.2f} │ {'-':^13} │ {'Mcal/dia':^18} │\n"
            text += f"│ {'EM Total':<28} │ {req.em:>13.2f} │ {'-':^13} │ {'Mcal/dia':^18} │\n"
            
            text += f"├{'─'*30}┼{'─'*15}┼{'─'*15}┼{'─'*20}┤\n"
            
            # Minerais
            text += f"│ {'Cálcio':<28} │ {req.ca:>13.1f} │ {(req.ca/1000/ms_total*100) if ms_total else 0:>13.2f} │ {'g/dia':^18} │\n"
            text += f"│ {'Fósforo':<28} │ {req.p:>13.1f} │ {(req.p/1000/ms_total*100) if ms_total else 0:>13.2f} │ {'g/dia':^18} │\n"
            
            # Se NRC 2021, mostrar nutrientes adicionais
            if model == "NRC 2021":
                text += f"│ {'Magnésio':<28} │ {req.mg:>13.1f} │ {(req.mg/1000/ms_total*100) if ms_total else 0:>13.2f} │ {'g/dia':^18} │\n"
                text += f"│ {'Potássio':<28} │ {req.k:>13.1f} │ {(req.k/1000/ms_total*100) if ms_total else 0:>13.2f} │ {'g/dia':^18} │\n"
                text += f"│ {'Sódio':<28} │ {req.na:>13.1f} │ {(req.na/1000/ms_total*100) if ms_total else 0:>13.2f} │ {'g/dia':^18} │\n"
                text += f"│ {'Cloro':<28} │ {req.cl:>13.1f} │ {(req.cl/1000/ms_total*100) if ms_total else 0:>13.2f} │ {'g/dia':^18} │\n"
                text += f"│ {'Enxofre':<28} │ {req.s:>13.1f} │ {(req.s/1000/ms_total*100) if ms_total else 0:>13.2f} │ {'g/dia':^18} │\n"
            
            text += f"└{'─'*30}┴{'─'*15}┴{'─'*15}┴{'─'*20}┘\n\n"
            
            self.result_text.insert(tk.END, text)
    
    def compare(self):
        if len(self.results) < 2:
            messagebox.showinfo("Aviso", "Calcule pelo menos 2 modelos para comparar")
            return
        
        self.result_text.delete(1.0, tk.END)
        
        header = f"{'='*120}\n"
        header += f"{'COMPARAÇÃO ENTRE MODELOS':^120}\n"
        header += f"{'='*120}\n\n"
        self.result_text.insert(tk.END, header)
        
        models = list(self.results.keys())
        col_width = 15
        
        # Cabeçalho da tabela
        table = f"{'Parâmetro':<25}"
        for model in models:
            table += f"│ {model[:col_width-2]:^{col_width-2}} "
        table += "│ CV%\n"
        table += "─"*25 + ("┼" + "─"*col_width)*len(models) + "┼─────\n"
        
        # Dados comparativos com CV%
        params = [
            ("MS (kg/dia)", lambda r: r.ms),
            ("MS (% PV)", lambda r: r.ms / float(self.entries['peso'].get()) * 100),
            ("NDT (kg/dia)", lambda r: r.ndt),
            ("NDT (% MS)", lambda r: (r.ndt/r.ms*100) if r.ms else 0),
            ("PB (kg/dia)", lambda r: r.pb),
            ("PB (% MS)", lambda r: (r.pb/r.ms*100) if r.ms else 0),
            ("PDR (% PB)", lambda r: (r.pdr/r.pb*100) if r.pb else 0),
            ("PNDR (% PB)", lambda r: (r.pndr/r.pb*100) if r.pb else 0),
            ("FDN (kg/dia)", lambda r: r.fdn),
            ("FDN (% MS)", lambda r: (r.fdn/r.ms*100) if r.ms else 0),
            ("Amido (% MS)", lambda r: (r.amido/r.ms*100) if r.ms else 0),
            ("Ca (g/dia)", lambda r: r.ca),
            ("P (g/dia)", lambda r: r.p),
            ("Ca:P", lambda r: (r.ca/r.p) if r.p else 0),
            ("ENl (Mcal/dia)", lambda r: r.enl),
            ("EM (Mcal/dia)", lambda r: r.em),
            ("ENl/MS (Mcal/kg)", lambda r: (r.enl/r.ms) if r.ms else 0)
        ]
        
        for param_name, getter in params:
            table += f"{param_name:<25}"
            values = []
            for model in models:
                value = getter(self.results[model])
                values.append(value)
                table += f"│ {value:^{col_width-2}.2f} "
            
            # Calcular CV
            if len(set(values)) > 1 and sum(values) > 0:
                mean = sum(values) / len(values)
                variance = sum((x - mean) ** 2 for x in values) / len(values)
                std_dev = math.sqrt(variance)
                cv = (std_dev / mean * 100) if mean != 0 else 0
                table += f"│ {cv:>4.1f}"
            else:
                table += "│  -  "
            
            table += "\n"
        
        self.result_text.insert(tk.END, table)
        
        # Análise de adequação
        analysis = "\n" + "="*80 + "\n"
        analysis += "ANÁLISE DE ADEQUAÇÃO E RECOMENDAÇÕES\n"
        analysis += "="*80 + "\n\n"
        
        # Verificar relações nutricionais
        for model, req in self.results.items():
            analysis += f"\n{model}:\n"
            analysis += "-"*40 + "\n"
            
            # Relação Ca:P
            ca_p = req.ca/req.p if req.p else 0
            if ca_p < 1.5:
                analysis += f"⚠️ Ca:P baixo ({ca_p:.2f}): Risco de problemas ósseos\n"
            elif ca_p > 2.5:
                analysis += f"⚠️ Ca:P alto ({ca_p:.2f}): Pode afetar absorção de P\n"
            else:
                analysis += f"✅ Ca:P adequado ({ca_p:.2f})\n"
            
            # % FDN
            fdn_ms = (req.fdn/req.ms*100) if req.ms else 0
            if fdn_ms < 28:
                analysis += f"⚠️ FDN baixo ({fdn_ms:.1f}%): Risco de acidose\n"
            elif fdn_ms > 38:
                analysis += f"⚠️ FDN alto ({fdn_ms:.1f}%): Pode limitar consumo\n"
            else:
                analysis += f"✅ FDN adequado ({fdn_ms:.1f}%)\n"
            
            # % Amido
            amido_ms = (req.amido/req.ms*100) if req.ms else 0
            if amido_ms > 28:
                analysis += f"⚠️ Amido alto ({amido_ms:.1f}%): Risco de acidose\n"
            elif amido_ms < 18:
                analysis += f"⚠️ Amido baixo ({amido_ms:.1f}%): Pode limitar produção\n"
            else:
                analysis += f"✅ Amido adequado ({amido_ms:.1f}%)\n"
        
        self.result_text.insert(tk.END, analysis)
    
    def export_pdf(self):
        """Exporta todos os dados para PDF"""
        if not self.results or not self.manual_diet:
            messagebox.showwarning("Aviso", "Calcule os requisitos e adicione ingredientes à dieta primeiro")
            return
        
        # Selecionar arquivo
        filename = filedialog.asksaveasfilename(
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
            initialfile=f"nutricao_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        )
        
        if not filename:
            return
        
        try:
            # Criar documento PDF
            doc = SimpleDocTemplate(filename, pagesize=landscape(letter))
            elements = []
            styles = getSampleStyleSheet()
            
            # Título
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Centralizado
            )
            elements.append(Paragraph("Relatório de Nutrição Bovina", title_style))
            elements.append(Spacer(1, 12))
            
            # Dados do animal
            animal_data = [
                ["DADOS DO ANIMAL", ""],
                ["Peso vivo (kg):", self.entries['peso'].get()],
                ["DEL (dias):", self.entries['del'].get()],
                ["Produção (kg/dia):", self.entries['prod'].get()],
                ["Gordura (%):", self.entries['gord'].get()],
                ["Proteína (%):", self.entries['prot'].get()],
                ["Nº Lactação:", self.entries['lact'].get()],
                ["Gestação (dias):", self.entries['gest'].get()],
                ["ECC (1-5):", self.entries['ecc'].get()],
                ["Raça:", self.raca_var.get()],
            ]
            
            animal_table = Table(animal_data, colWidths=[3*inch, 2*inch])
            animal_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            elements.append(animal_table)
            elements.append(Spacer(1, 20))
            
            # Requisitos nutricionais
            elements.append(Paragraph("REQUISITOS NUTRICIONAIS CALCULADOS", styles['Heading2']))
            elements.append(Spacer(1, 12))
            
            for model, req in self.results.items():
                elements.append(Paragraph(f"Modelo: {model}", styles['Heading3']))
                
                req_data = [
                    ["Nutriente", "Quantidade", "Unidade"],
                    ["Matéria Seca", f"{req.ms:.2f}", "kg/dia"],
                    ["NDT", f"{req.ndt:.2f}", "kg/dia"],
                    ["Proteína Bruta", f"{req.pb:.2f}", "kg/dia"],
                    ["PDR", f"{req.pdr:.2f}", "kg/dia"],
                    ["PNDR", f"{req.pndr:.2f}", "kg/dia"],
                    ["Extrato Etéreo", f"{req.ee:.2f}", "kg/dia"],
                    ["FDN", f"{req.fdn:.2f}", "kg/dia"],
                    ["FDA", f"{req.fda:.2f}", "kg/dia"],
                    ["Amido", f"{req.amido:.2f}", "kg/dia"],
                    ["Cálcio", f"{req.ca:.1f}", "g/dia"],
                    ["Fósforo", f"{req.p:.1f}", "g/dia"],
                    ["ENl (Produção)", f"{req.enl:.2f}", "Mcal/dia"],
                    ["EM Total", f"{req.em:.2f}", "Mcal/dia"],
                ]
                
                if model == "NRC 2021":
                    req_data.extend([
                        ["Magnésio", f"{req.mg:.1f}", "g/dia"],
                        ["Potássio", f"{req.k:.1f}", "g/dia"],
                        ["Sódio", f"{req.na:.1f}", "g/dia"],
                        ["Cloro", f"{req.cl:.1f}", "g/dia"],
                        ["Enxofre", f"{req.s:.1f}", "g/dia"],
                    ])
                
                req_table = Table(req_data, colWidths=[2.5*inch, 1.5*inch, 1*inch])
                req_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(req_table)
                elements.append(Spacer(1, 12))
            
            # Ingredientes da dieta
            elements.append(Paragraph("INGREDIENTES DA DIETA", styles['Heading2']))
            elements.append(Spacer(1, 12))
            
            diet_data = [["Ingrediente", "MN (kg/dia)", "MS (kg/dia)", "% MS", "R$/dia"]]
            total_mn = 0
            total_ms = 0
            total_cost = 0
            
            for ing_name, qtd_mn in self.manual_diet.items():
                ing = self.banco.get_all().get(ing_name)
                if ing:
                    qtd_ms = qtd_mn * (ing.ms / 100)
                    perc_ms = (qtd_ms / sum(self.manual_diet.values()) * 100) if self.manual_diet else 0
                    cost = qtd_mn * ing.preco
                    
                    total_mn += qtd_mn
                    total_ms += qtd_ms
                    total_cost += cost
                    
                    diet_data.append([ing_name, f"{qtd_mn:.2f}", f"{qtd_ms:.2f}", f"{perc_ms:.1f}", f"R$ {cost:.2f}"])
            
            diet_data.append(["TOTAL", f"{total_mn:.2f}", f"{total_ms:.2f}", "100.0", f"R$ {total_cost:.2f}"])
            
            diet_table = Table(diet_data, colWidths=[2.5*inch, 1.2*inch, 1.2*inch, 0.8*inch, 1.3*inch])
            diet_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-2, -1), colors.beige),
                ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
                ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            elements.append(diet_table)
            elements.append(Spacer(1, 20))
            
            # Análise nutricional da dieta
            elements.append(Paragraph("ANÁLISE NUTRICIONAL DA DIETA", styles['Heading2']))
            elements.append(Spacer(1, 12))
            
            # Calcular nutrientes da dieta
            modelo = self.diet_model_var.get()
            if modelo in self.results:
                req = self.results[modelo]
                formulator = DietFormulator(req, self.banco.get_all())
                nutrients = formulator.calculate_nutrients(self.manual_diet)
                
                analysis_data = [
                    ["Nutriente", "Requerido", "Fornecido", "Diferença", "%"],
                    ["MS (kg/dia)", f"{req.ms:.2f}", f"{nutrients['ms']:.2f}", f"{nutrients['ms']-req.ms:+.2f}", 
                     f"{(nutrients['ms']/req.ms*100):.1f}" if req.ms > 0 else "0.0"],
                    ["NDT (kg/dia)", f"{req.ndt:.2f}", f"{nutrients['ndt']:.2f}", f"{nutrients['ndt']-req.ndt:+.2f}",
                     f"{(nutrients['ndt']/req.ndt*100):.1f}" if req.ndt > 0 else "0.0"],
                    ["PB (kg/dia)", f"{req.pb:.2f}", f"{nutrients['pb']:.2f}", f"{nutrients['pb']-req.pb:+.2f}",
                     f"{(nutrients['pb']/req.pb*100):.1f}" if req.pb > 0 else "0.0"],
                    ["PDR (kg/dia)", f"{req.pdr:.2f}", f"{nutrients['pdr']:.2f}", f"{nutrients['pdr']-req.pdr:+.2f}",
                     f"{(nutrients['pdr']/req.pdr*100):.1f}" if req.pdr > 0 else "0.0"],
                    ["FDN (kg/dia)", f"{req.fdn:.2f}", f"{nutrients['fdn']:.2f}", f"{nutrients['fdn']-req.fdn:+.2f}",
                     f"{(nutrients['fdn']/req.fdn*100):.1f}" if req.fdn > 0 else "0.0"],
                    ["Amido (kg/dia)", f"{req.amido:.2f}", f"{nutrients['amido']:.2f}", f"{nutrients['amido']-req.amido:+.2f}",
                     f"{(nutrients['amido']/req.amido*100):.1f}" if req.amido > 0 else "0.0"],
                    ["Ca (g/dia)", f"{req.ca:.1f}", f"{nutrients['ca']*1000:.1f}", f"{nutrients['ca']*1000-req.ca:+.1f}",
                     f"{(nutrients['ca']*1000/req.ca*100):.1f}" if req.ca > 0 else "0.0"],
                    ["P (g/dia)", f"{req.p:.1f}", f"{nutrients['p']*1000:.1f}", f"{nutrients['p']*1000-req.p:+.1f}",
                     f"{(nutrients['p']*1000/req.p*100):.1f}" if req.p > 0 else "0.0"],
                    ["Custo (R$/dia)", "0.00", f"{nutrients['custo']:.2f}", f"{nutrients['custo']:+.2f}", "-"],
                ]
                
                analysis_table = Table(analysis_data, colWidths=[1.8*inch, 1.2*inch, 1.2*inch, 1.2*inch, 0.8*inch])
                analysis_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(analysis_table)
            
            # Data e hora
            elements.append(Spacer(1, 20))
            elements.append(Paragraph(f"Gerado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}", styles['Normal']))
            
            # Construir PDF
            doc.build(elements)
            
            messagebox.showinfo("Sucesso", f"Relatório exportado para {filename}")
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao exportar PDF: {str(e)}")
    
    def clear(self):
        self.result_text.delete(1.0, tk.END)
        self.results = {}
        self.diets = {}
    
    def update_ingredient_selector(self):
        """Atualiza a lista de ingredientes no seletor"""
        ingredientes = list(self.banco.get_all().keys())
        self.ing_selector['values'] = ingredientes
        if ingredientes:
            self.ing_selector.current(0)
    
    def add_manual_ingredient(self):
        """Adiciona um ingrediente manualmente à dieta em MN"""
        try:
            ing_nome = self.ing_selector.get()
            qtd_mn = float(self.qtd_entry.get())
            
            if not ing_nome:
                messagebox.showwarning("Aviso", "Selecione um ingrediente")
                return
            
            if qtd_mn <= 0:
                messagebox.showwarning("Aviso", "Quantidade deve ser maior que zero")
                return
            
            # Adicionar à dieta manual
            if ing_nome in self.manual_diet:
                self.manual_diet[ing_nome] += qtd_mn
            else:
                self.manual_diet[ing_nome] = qtd_mn
            
            # Atualizar display
            self.display_manual_diet()
            
            # Limpar campo
            self.qtd_entry.delete(0, tk.END)
            
        except ValueError:
            messagebox.showerror("Erro", "Quantidade inválida")
    
    def remove_selected_ingredient(self):
        """Remove o ingrediente selecionado da dieta manual"""
        selected = self.diet_tree.selection()
        if not selected:
            messagebox.showwarning("Aviso", "Selecione um ingrediente para remover")
            return
        
        item = self.diet_tree.item(selected[0])
        ing_nome = item['values'][0]
        
        # Confirmar remoção
        if messagebox.askyesno("Confirmar", f"Remover '{ing_nome}' da dieta?"):
            if ing_nome in self.manual_diet:
                del self.manual_diet[ing_nome]
                self.display_manual_diet()
    
    def clear_manual_diet(self):
        """Limpa a dieta manual"""
        if messagebox.askyesno("Confirmar", "Limpar todos os ingredientes da dieta?"):
            self.manual_diet = {}
            self.display_manual_diet()
            self.diet_analysis.delete(1.0, tk.END)
    
    def display_manual_diet(self):
        """Exibe a dieta manual na treeview"""
        # Limpar treeview
        for item in self.diet_tree.get_children():
            self.diet_tree.delete(item)
        
        total_mn = sum(self.manual_diet.values())
        
        for ing_name, qtd_mn in self.manual_diet.items():
            ing = self.banco.get_all().get(ing_name)
            if ing:
                qtd_ms = qtd_mn * (ing.ms / 100)  # Converter MN para MS
                perc_ms = (qtd_mn / total_mn) * 100 if total_mn > 0 else 0
                custo_dia = qtd_mn * ing.preco
                
                self.diet_tree.insert('', 'end', values=(
                    ing_name,
                    f"{qtd_mn:.2f}",
                    f"{qtd_ms:.2f}",
                    f"{perc_ms:.1f}",
                    f"R$ {custo_dia:.2f}"
                ))
    
    def calculate_manual_analysis(self):
        """Calcula a análise nutricional completa da dieta manual"""
        if not self.manual_diet:
            messagebox.showwarning("Aviso", "Adicione ingredientes à dieta primeiro")
            return
        
        # Obter modelo para comparação
        modelo = self.diet_model_var.get()
        if modelo not in self.results:
            messagebox.showwarning("Aviso", f"Calcule os requisitos para o modelo {modelo} primeiro")
            return
        
        req = self.results[modelo]
        
        # Calcular nutrientes da dieta manual
        formulator = DietFormulator(req, self.banco.get_all())
        nutrients = formulator.calculate_nutrients(self.manual_diet)
        
        # Exibir análise completa
        self.display_complete_analysis(req, nutrients)
    
    def display_complete_analysis(self, req, nutrients):
        """Exibe análise nutricional completa da dieta"""
        self.diet_analysis.delete(1.0, tk.END)
        
        analysis = "ANÁLISE NUTRICIONAL COMPLETA DA DIETA\n"
        analysis += "="*80 + "\n\n"
        
        # Comparação requisitos vs fornecido
        analysis += "NUTRIENTE\tREQUERIDO\tFORNECIDO\tDIFERENÇA\t%\n"
        analysis += "-"*80 + "\n"
        
        # Todos os nutrientes
        nutrientes_data = [
            ("MS (kg/dia)", req.ms, nutrients["ms"]),
            ("NDT (kg/dia)", req.ndt, nutrients["ndt"]),
            ("PB (kg/dia)", req.pb, nutrients["pb"]),
            ("PDR (kg/dia)", req.pdr, nutrients["pdr"]),
            ("PNDR (kg/dia)", req.pndr, nutrients["pndr"]),
            ("EE (kg/dia)", req.ee, nutrients["ee"]),
            ("FDN (kg/dia)", req.fdn, nutrients["fdn"]),
            ("FDA (kg/dia)", req.fda, nutrients["fda"]),
            ("Amido (kg/dia)", req.amido, nutrients["amido"]),
            ("FDN efetiva (kg/dia)", req.pndf, nutrients["fdn"] * 0.75),  # Estimativa
            ("Ca (g/dia)", req.ca, nutrients["ca"]*1000),
            ("P (g/dia)", req.p, nutrients["p"]*1000),
            ("Mg (g/dia)", req.mg if hasattr(req, 'mg') else 0, nutrients["mg"]*1000),
            ("K (g/dia)", req.k if hasattr(req, 'k') else 0, nutrients["k"]*1000),
            ("Na (g/dia)", req.na if hasattr(req, 'na') else 0, nutrients["na"]*1000),
            ("Cl (g/dia)", req.cl if hasattr(req, 'cl') else 0, nutrients["cl"]*1000),
            ("S (g/dia)", req.s if hasattr(req, 's') else 0, nutrients["s"]*1000),
            ("ENl (Mcal/dia)", req.enl, nutrients["enl"]),
            ("EM (Mcal/dia)", req.em, nutrients["enl"] / 0.65),  # Estimativa
            ("Custo (R$/dia)", 0, nutrients["custo"])
        ]
        
        for nome, req_val, forn in nutrientes_data:
            diff = forn - req_val
            perc = (forn / req_val * 100) if req_val > 0 else 0
            analysis += f"{nome}\t{req_val:.2f}\t{forn:.2f}\t{diff:+.2f}\t{perc:.1f}%\n"
        
        # Análise de adequação
        analysis += "\n" + "="*80 + "\n"
        analysis += "ANÁLISE DE ADEQUAÇÃO:\n"
        analysis += "-"*80 + "\n"
        
        # Verificar relações importantes
        if nutrients["p"] > 0:
            ca_p = (nutrients["ca"]*1000) / (nutrients["p"]*1000)
            analysis += f"Relação Ca:P: {ca_p:.2f} "
            if 1.5 <= ca_p <= 2.0:
                analysis += "(Ideal)\n"
            else:
                analysis += "(Fora do ideal - recomendado 1.5-2.0)\n"
        
        if nutrients["ms"] > 0:
            fdn_perc = (nutrients["fdn"] / nutrients["ms"]) * 100
            analysis += f"FDN (% MS): {fdn_perc:.1f}% "
            if 28 <= fdn_perc <= 35:
                analysis += "(Adequado)\n"
            else:
                analysis += "(Fora do ideal - recomendado 28-35%)\n"
            
            amido_perc = (nutrients["amido"] / nutrients["ms"]) * 100
            analysis += f"Amido (% MS): {amido_perc:.1f}% "
            if 20 <= amido_perc <= 28:
                analysis += "(Adequado)\n"
            else:
                analysis += "(Fora do ideal - recomendado 20-28%)\n"
        
        # Balanceamento de proteína
        if nutrients["pb"] > 0:
            pdr_perc = (nutrients["pdr"] / nutrients["pb"]) * 100
            analysis += f"PDR (% PB): {pdr_perc:.1f}% "
            if 60 <= pdr_perc <= 70:
                analysis += "(Adequado)\n"
            else:
                analysis += "(Fora do ideal - recomendado 60-70%)\n"
        
        # Eficiência alimentar
        if nutrients["ms"] > 0 and req.prod > 0:
            eficiencia = req.prod / nutrients["ms"]
            analysis += f"\nEficiência alimentar: {eficiencia:.2f} kg leite/kg MS "
            if eficiencia > 1.2:
                analysis += "(Excelente)\n"
            elif eficiencia > 1.0:
                analysis += "(Boa)\n"
            else:
                analysis += "(Pode ser melhorada)\n"
        
        # Custo por kg de leite
        if nutrients["custo"] > 0 and req.prod > 0:
            custo_leite = nutrients["custo"] / req.prod
            analysis += f"Custo por kg de leite: R$ {custo_leite:.2f}\n"
        
        self.diet_analysis.insert(tk.END, analysis)
    
    def refresh_ingredients(self):
        """Atualiza a lista de ingredientes na Treeview"""
        # Limpar a treeview
        for item in self.ing_tree.get_children():
            self.ing_tree.delete(item)
        
        # Adicionar ingredientes do banco
        for nome, ing in self.banco.get_all().items():
            self.ing_tree.insert('', 'end', values=(
                nome,
                f"{ing.ms:.1f}",
                f"{ing.pb:.1f}",
                f"{ing.fdn:.1f}",
                f"{ing.amido:.1f}",
                f"{ing.enl:.2f}",
                f"{ing.preco:.2f}",
                ing.tipo
            ))
        
        # Atualizar seletor de ingredientes
        self.update_ingredient_selector()
    
    def add_ingredient(self):
        """Adiciona um novo ingrediente ao banco"""
        try:
            # Coletar dados do formulário
            ing_data = {}
            for key, entry in self.ing_entries.items():
                ing_data[key] = float(entry.get())
            
            # Criar ingrediente
            ing = Ingrediente(
                nome=ing_data["nome"],
                ms=ing_data["ms"],
                pb=ing_data["pb"],
                pdr=ing_data["pdr"],
                ee=ing_data["ee"],
                fdn=ing_data["fdn"],
                fda=ing_data["fda"],
                amido=ing_data["amido"],
                enl=ing_data["enl"],
                ca=ing_data["ca"],
                p=ing_data["p"],
                preco=ing_data["preco"],
                tipo=self.ing_tipo_var.get()
            )
            
            # Adicionar ao banco
            self.banco.add_custom(ing)
            
            # Atualizar lista
            self.refresh_ingredients()
            
            # Limpar campos
            for entry in self.ing_entries.values():
                entry.delete(0, tk.END)
            
            messagebox.showinfo("Sucesso", f"Ingrediente '{ing.nome}' adicionado com sucesso!")
            
        except ValueError:
            messagebox.showerror("Erro", "Verifique os valores inseridos")
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao adicionar ingrediente: {str(e)}")
    
    def remove_ingredient(self):
        """Remove um ingrediente selecionado"""
        selected = self.ing_tree.selection()
        if not selected:
            messagebox.showwarning("Aviso", "Selecione um ingrediente para remover")
            return
        
        item = self.ing_tree.item(selected[0])
        nome = item['values'][0]
        
        # Confirmar remoção
        if messagebox.askyesno("Confirmar", f"Remover '{nome}' do banco de ingredientes?"):
            # Remover do banco de custom
            if nome in self.banco.custom_ingredientes:
                del self.banco.custom_ingredientes[nome]
                self.refresh_ingredients()
                messagebox.showinfo("Sucesso", "Ingrediente removido com sucesso!")
            else:
                messagebox.showwarning("Aviso", "Apenas ingredientes personalizados podem ser removidos")
    
    def production_projection(self):
        """Projeção de produção baseada na dieta"""
        if not self.manual_diet:
            messagebox.showwarning("Aviso", "Adicione ingredientes à dieta primeiro")
            return
        
        self.analysis_text.delete(1.0, tk.END)
        
        # Obter modelo para comparação
        modelo = self.diet_model_var.get()
        if modelo not in self.results:
            messagebox.showwarning("Aviso", f"Calcule os requisitos para o modelo {modelo} primeiro")
            return
        
        req = self.results[modelo]
        
        # Calcular nutrientes da dieta manual
        formulator = DietFormulator(req, self.banco.get_all())
        nutrients = formulator.calculate_nutrients(self.manual_diet)
        
        # Projeção de produção
        analysis = "PROJEÇÃO DE PRODUÇÃO\n"
        analysis += "="*60 + "\n\n"
        
        # Fatores limitantes
        fatores = []
        
        # Verificar energia
        if nutrients["enl"] < req.enl:
            deficit_energia = (req.enl - nutrients["enl"]) / req.enl * 100
            fatores.append(f"Energia: déficit de {deficit_energia:.1f}%")
        
        # Verificar proteína
        if nutrients["pb"] < req.pb:
            deficit_proteina = (req.pb - nutrients["pb"]) / req.pb * 100
            fatores.append(f"Proteína: déficit de {deficit_proteina:.1f}%")
        
        # Verificar fibra
        fdn_perc = (nutrients["fdn"] / nutrients["ms"]) * 100 if nutrients["ms"] > 0 else 0
        if fdn_perc < 25:
            fatores.append("FDN: risco de acidose (<25%)")
        elif fdn_perc > 40:
            fatores.append("FDN: pode limitar consumo (>40%)")
        
        # Verificar amido
        amido_perc = (nutrients["amido"] / nutrients["ms"]) * 100 if nutrients["ms"] > 0 else 0
        if amido_perc > 30:
            fatores.append("Amido: risco de acidose (>30%)")
        
        if fatores:
            analysis += "FATORES LIMITANTES IDENTIFICADOS:\n"
            for fator in fatores:
                analysis += f"• {fator}\n"
            analysis += "\n"
        
        # Cálculo de produção potencial
        # Baseado no primeiro nutriente limitante (energia ou proteína)
        if nutrients["enl"] > 0 and nutrients["pb"] > 0:
            # Potencial baseado em energia
            prod_energia = (nutrients["enl"] * 0.65 - req.em * 0.35) / 0.0929  # Simplificado
            
            # Potencial baseado em proteína
            prod_proteina = (nutrients["pb"] * 0.67 * 1000 / req.prot) / 10  # Simplificado
            
            # Potencial real (limitado pelo menor)
            prod_potencial = min(prod_energia, prod_proteina, req.prod * 1.2)  # Máximo 20% acima da atual
            
            analysis += f"Produção atual: {req.prod:.1f} kg/dia\n"
            analysis += f"Produção potencial: {prod_potencial:.1f} kg/dia\n"
            analysis += f"Ganho potencial: {prod_potencial - req.prod:+.1f} kg/dia ({(prod_potencial/req.prod-1)*100:+.1f}%)\n\n"
            
            # Recomendações
            analysis += "RECOMENDAÇÕES PARA MELHORAR PRODUÇÃO:\n"
            
            if nutrients["enl"] < req.enl * 0.95:
                analysis += "• Aumentar concentrados energéticos (milho, polpa cítrica)\n"
            
            if nutrients["pb"] < req.pb * 0.95:
                analysis += "• Aumentar concentrados proteicos (farelo de soja)\n"
            
            if fdn_perc < 28:
                analysis += "• Aumentar volumosos de qualidade\n"
            
            if amido_perc > 28:
                analysis += "• Reduzir amido e aumentar fibra efetiva\n"
            
            # Balanceamento mineral
            if nutrients["ca"] * 1000 < req.ca * 0.9 or nutrients["p"] * 1000 < req.p * 0.9:
                analysis += "• Ajustar suplementação mineral\n"
        
        self.analysis_text.insert(tk.END, analysis)
    
    def economic_analysis(self):
        """Análise econômica da dieta"""
        if not self.manual_diet:
            messagebox.showwarning("Aviso", "Adicione ingredientes à dieta primeiro")
            return
        
        self.analysis_text.delete(1.0, tk.END)
        
        # Obter modelo para comparação
        modelo = self.diet_model_var.get()
        if modelo not in self.results:
            messagebox.showwarning("Aviso", f"Calcule os requisitos para o modelo {modelo} primeiro")
            return
        
        req = self.results[modelo]
        
        # Calcular nutrientes da dieta manual
        formulator = DietFormulator(req, self.banco.get_all())
        nutrients = formulator.calculate_nutrients(self.manual_diet)
        
        # Análise econômica
        analysis = "ANÁLISE ECONÔMICA\n"
        analysis += "="*60 + "\n\n"
        
        # Custos
        total_custo = nutrients["custo"]
        custo_ms = total_custo / nutrients["ms"] if nutrients["ms"] > 0 else 0
        custo_leite = total_custo / req.prod if req.prod > 0 else 0
        
        analysis += f"Custo total da dieta: R$ {total_custo:.2f}/dia\n"
        analysis += f"Custo por kg de MS: R$ {custo_ms:.3f}\n"
        analysis += f"Custo por kg de leite: R$ {custo_leite:.3f}\n\n"
        
        # Análise de rentabilidade
        preco_leite = 2.50  # R$/kg (pode ser ajustado)
        receita_leite = req.prod * preco_leite
        margem_bruta = receita_leite - total_custo
        margem_percentual = (margem_bruta / receita_leite * 100) if receita_leite > 0 else 0
        
        analysis += f"Receita com leite (R$ {preco_leite:.2f}/kg): R$ {receita_leite:.2f}/dia\n"
        analysis += f"Margem bruta: R$ {margem_bruta:.2f}/dia ({margem_percentual:+.1f}%)\n\n"
        
        # Análise de ingredientes
        analysis += "CUSTO POR INGREDIENTE:\n"
        analysis += "-"*50 + "\n"
        
        custo_ingredientes = []
        for ing_name, qtd_mn in self.manual_diet.items():
            ing = self.banco.get_all().get(ing_name)
            if ing:
                custo_ing = qtd_mn * ing.preco
                perc_total = (custo_ing / total_custo * 100) if total_custo > 0 else 0
                custo_ingredientes.append((ing_nome, custo_ing, perc_total))
        
        # Ordenar por custo
        custo_ingredientes.sort(key=lambda x: x[1], reverse=True)
        
        for nome, custo, perc in custo_ingredientes:
            analysis += f"{nome}: R$ {custo:.2f} ({perc:.1f}%)\n"
        
        # Recomendações econômicas
        analysis += "\nRECOMENDAÇÕES ECONÔMICAS:\n"
        
        # Identificar ingredientes mais caros
        if custo_ingredientes:
            mais_caro = custo_ingredientes[0]
            if mais_caro[2] > 40:  # Se representa mais de 40% do custo
                analysis += f"• '{mais_caro[0]}' representa {mais_caro[2]:.1f}% do custo total. "
                analysis += "Considere alternativas mais econômicas.\n"
        
        # Avaliar custo por kg de leite
        if custo_leite > preco_leite * 0.7:  # Se custo > 70% do preço do leite
            analysis += "• Custo de alimentação está alto. "
            analysis += "Revise composição da dieta e negocie preços dos insumos.\n"
        
        # Sugerir otimização
        if nutrients["ms"] > 0:
            ndt_ms = (nutrients["ndt"] / nutrients["ms"]) * 100
            if ndt_ms < 65:  # Baixa densidade energética
                analysis += "• Dieta com baixa densidade energética. "
                analysis += "Considere incluir concentrados para melhorar eficiência.\n"
        
        self.analysis_text.insert(tk.END, analysis)

if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()