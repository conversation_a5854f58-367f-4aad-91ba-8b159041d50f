import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import math
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

# Função principal de cálculo de exigências nutricionais
def calcular_exigencias_nrc_expandido(peso_vivo, producao_leite, gordura_leite, proteina_leite, 
                                     dias_lactacao, num_lactacoes, peso_parto, dias_gestacao,
                                     raca, temperatura, umidade, manejo):
    """
    Calcula exigências nutricionais expandidas para vacas leiteiras (NRC 2001 e outros).
    Considera raça, condições ambientais e manejo.
    """
    # Fatores de ajuste
    fator_raca = {"Holandesa": 1.0, "Jersey": 1.1, "Gir": 1.05, "Girolando": 1.02}
    fator_raca_nel_pm = fator_raca.get(raca, 1.0)
    
    # Índice de Temperatura e Umidade (ITH)
    ith = (0.72 * (temperatura + umidade) + 40.6)
    fator_estresse_calor = 1.0
    if ith > 72:
        # Aumentar NEL em 5% para cada 10 unidades acima de 72
        fator_estresse_calor_nel = 1.0 + (0.05 * ((ith - 72) / 10))
        # Aumentar K em 1% e Na em 0.5% para cada 10 unidades acima de 72
        fator_estresse_calor_k = 1.0 + (0.01 * ((ith - 72) / 10))
        fator_estresse_calor_na = 1.0 + (0.005 * ((ith - 72) / 10))
    else:
        fator_estresse_calor_nel = 1.0
        fator_estresse_calor_k = 1.0
        fator_estresse_calor_na = 1.0
    
    # Fator de manejo (atividade)
    fator_manejo = {"Confinamento": 1.0, "Pasto": 1.1, "Semi-confinamento": 1.05}
    fator_atividade = fator_manejo.get(manejo, 1.0)
    
    # --- Cálculo de NEL (Energia Líquida de Lactação) ---
    NEL_mant = 0.08 * (peso_vivo ** 0.75) * fator_atividade
    NEL_lact = producao_leite * (0.0929 * gordura_leite + 0.0547 * proteina_leite + 0.192)
    NEL_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        t = dias_gestacao - 190
        NEL_gest = 0.00318 * peso_parto * math.exp(0.018 * t)
    NEL_total = (NEL_mant + NEL_lact + NEL_gest) * fator_raca_nel_pm * fator_estresse_calor_nel
    
    # --- Cálculo de PM (Proteína Metabolizável) ---
    PM_mant = 3.8 * ((peso_vivo / 1000) ** 0.75)
    PM_lact = producao_leite * (proteina_leite * 10) * 1.25
    PM_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        PM_gest = 1.136 * ((peso_parto / 1000) ** 0.7) * math.exp(-0.00203 * (dias_gestacao - 190))
    PM_total = (PM_mant + PM_lact + PM_gest) * fator_raca_nel_pm
    
    # --- Minerais (Cálcio e Fósforo) ---
    Ca_mant = peso_vivo * 0.0154
    Ca_lact = producao_leite * 1.22
    Ca_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        Ca_gest = 0.0076 * peso_parto * math.exp(0.014 * (dias_gestacao - 190))
    Ca_total = Ca_mant + Ca_lact + Ca_gest
    
    P_mant = peso_vivo * 0.014
    P_lact = producao_leite * 0.9
    P_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        P_gest = 0.0047 * peso_parto * math.exp(0.011 * (dias_gestacao - 190))
    P_total = P_mant + P_lact + P_gest
    
    # --- Vitaminas ---
    VitA_mant = 110 * peso_vivo
    VitA_lact = 30 * producao_leite
    VitA_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        # Peso fetal estimado: 0.028 * (dias_gestacao - 190) ** 2
        peso_fetal = 0.028 * ((dias_gestacao - 190) ** 2)
        VitA_gest = 40 * peso_fetal
    VitA_total = VitA_mant + VitA_lact + VitA_gest
    
    VitD_mant = 30 * peso_vivo
    VitD_lact = 15 * producao_leite
    VitD_total = VitD_mant + VitD_lact
    
    VitE_mant = 1.5 * peso_vivo
    VitE_lact = 0.5 * producao_leite
    VitE_total = VitE_mant + VitE_lact
    
    # --- Outros Minerais ---
    Mg_mant = 0.004 * peso_vivo
    Mg_lact = 0.12 * producao_leite
    Mg_total = Mg_mant + Mg_lact
    
    K_mant = 0.039 * peso_vivo
    K_lact = 1.5 * producao_leite
    K_total = (K_mant + K_lact) * fator_estresse_calor_k
    
    Na_mant = 0.015 * peso_vivo
    Na_lact = 1.0 * producao_leite
    Na_total = (Na_mant + Na_lact) * fator_estresse_calor_na
    
    Cl_mant = 0.022 * peso_vivo
    Cl_lact = 1.2 * producao_leite
    Cl_total = Cl_mant + Cl_lact
    
    S_mant = 0.0006 * peso_vivo
    S_lact = 0.3 * producao_leite
    S_total = S_mant + S_lact
    
    Cu_mant = 0.1 * peso_vivo
    Cu_lact = 0.5 * producao_leite
    Cu_total = Cu_mant + Cu_lact
    
    Zn_mant = 0.5 * peso_vivo
    Zn_lact = 15 * producao_leite
    Zn_total = Zn_mant + Zn_lact
    
    Se_mant = 0.0006 * peso_vivo
    Se_lact = 0.05 * producao_leite
    Se_total = Se_mant + Se_lact
    
    I_mant = 0.006 * peso_vivo
    I_lact = 0.1 * producao_leite
    I_total = I_mant + I_lact
    
    Co_mant = 0.0001 * peso_vivo
    Co_lact = 0.01 * producao_leite
    Co_total = Co_mant + Co_lact
    
    Mn_mant = 0.002 * peso_vivo
    Mn_lact = 0.2 * producao_leite
    Mn_total = Mn_mant + Mn_lact
    
    # --- FDN e CNF ---
    # FDN mínimo: 1.2% do PV, mas pode ser ajustado pela produção
    FDN_min = 0.012 * peso_vivo  # kg/dia
    # CNF: não há exigência, mas pode ser estimado como 35-40% da MS
    
    return {
        "Energia (NEL, Mcal/dia)": round(NEL_total, 2),
        "Proteína Metabolizável (PM, g/dia)": round(PM_total, 0),
        "Cálcio (g/dia)": round(Ca_total, 1),
        "Fósforo (g/dia)": round(P_total, 1),
        "Magnésio (g/dia)": round(Mg_total, 1),
        "Potássio (g/dia)": round(K_total, 1),
        "Sódio (g/dia)": round(Na_total, 1),
        "Cloro (g/dia)": round(Cl_total, 1),
        "Enxofre (g/dia)": round(S_total, 1),
        "Cobre (mg/dia)": round(Cu_total, 0),
        "Zinco (mg/dia)": round(Zn_total, 0),
        "Selênio (mg/dia)": round(Se_total, 2),
        "Iodo (mg/dia)": round(I_total, 2),
        "Cobalto (mg/dia)": round(Co_total, 3),
        "Manganês (mg/dia)": round(Mn_total, 0),
        "Vitamina A (UI/dia)": round(VitA_total, 0),
        "Vitamina D (UI/dia)": round(VitD_total, 0),
        "Vitamina E (UI/dia)": round(VitE_total, 0),
        "FDN mínimo (kg/dia)": round(FDN_min, 1)
    }

# Função para gerar relatório PDF
def gerar_relatorio_pdf(resultados, dados_vaca, nome_arquivo=None):
    if not nome_arquivo:
        nome_arquivo = filedialog.asksaveasfilename(
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf")],
            title="Salvar Relatório"
        )
        if not nome_arquivo:
            return
    
    doc = SimpleDocTemplate(nome_arquivo, pagesize=letter)
    elements = []
    styles = getSampleStyleSheet()
    
    # Título
    elements.append(Paragraph("Relatório de Exigências Nutricionais - NRC 2001", styles['Title']))
    
    # Dados da vaca
    dados_texto = f"""
    <b>Dados da Vaca:</b><br/>
    Peso Vivo: {dados_vaca['peso_vivo']} kg<br/>
    Produção de Leite: {dados_vaca['producao_leite']} kg/dia<br/>
    Gordura no Leite: {dados_vaca['gordura_leite']}%<br/>
    Proteína no Leite: {dados_vaca['proteina_leite']}%<br/>
    Dias de Lactação: {dados_vaca['dias_lactacao']}<br/>
    Raça: {dados_vaca['raca']}<br/>
    Manejo: {dados_vaca['manejo']}<br/>
    Temperatura: {dados_vaca['temperatura']}°C<br/>
    Umidade: {dados_vaca['umidade']}%<br/>
    """
    elements.append(Paragraph(dados_texto, styles['Normal']))
    
    # Tabela de resultados
    data = [["Nutriente", "Exigência Diária"]]
    for nutriente, valor in resultados.items():
        data.append([nutriente, str(valor)])
    
    t = Table(data)
    t.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(t)
    doc.build(elements)
    messagebox.showinfo("Sucesso", f"Relatório salvo em: {nome_arquivo}")

# Função para criar gráficos
def criar_graficos(resultados, dados_vaca, root):
    # Criar janela para gráficos
    janela_graficos = tk.Toplevel(root)
    janela_graficos.title("Gráficos de Exigências Nutricionais")
    janela_graficos.geometry("1000x700")
    
    # Criar notebook para abas
    notebook = ttk.Notebook(janela_graficos)
    notebook.pack(fill='both', expand=True)
    
    # Aba 1: Gráfico de barras (exigências atuais)
    aba_barras = ttk.Frame(notebook)
    notebook.add(aba_barras, text="Exigências Atuais")
    
    # Selecionar nutrientes principais para o gráfico
    nutrientes_principais = {
        "Energia (NEL, Mcal/dia)": resultados["Energia (NEL, Mcal/dia)"],
        "Proteína Metabolizável (PM, g/dia)": resultados["Proteína Metabolizável (PM, g/dia)"] / 1000,  # Converter para kg
        "Cálcio (g/dia)": resultados["Cálcio (g/dia)"],
        "Fósforo (g/dia)": resultados["Fósforo (g/dia)"],
        "Magnésio (g/dia)": resultados["Magnésio (g/dia)"],
        "Potássio (g/dia)": resultados["Potássio (g/dia)"]
    }
    
    fig1, ax1 = plt.subplots(figsize=(10, 6))
    nutrientes = list(nutrientes_principais.keys())
    valores = list(nutrientes_principais.values())
    
    bars = ax1.bar(nutrientes, valores, color='skyblue')
    ax1.set_ylabel('Exigência Diária')
    ax1.set_title('Exigências Nutricionais Principais')
    plt.xticks(rotation=45, ha='right')
    
    # Adicionar valores nas barras
    for bar in bars:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.2f}', ha='center', va='bottom')
    
    canvas1 = FigureCanvasTkAgg(fig1, master=aba_barras)
    canvas1.draw()
    canvas1.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    # Aba 2: Gráfico de linha (exigências ao longo do tempo)
    aba_linha = ttk.Frame(notebook)
    notebook.add(aba_linha, text="Exigências ao Longo do Tempo")
    
    # Simular exigências ao longo da lactação (305 dias)
    dias = np.arange(1, 306)
    
    # Simular variação de produção de leite (curva típica de lactação)
    producao_sim = dados_vaca['producao_leite'] * np.exp(-0.05 * (dias - 60)**2 / 1000)
    
    # Calcular exigências para cada dia
    nel_sim = []
    pm_sim = []
    ca_sim = []
    
    for dia in dias:
        # Atualizar produção de leite simulada
        prod = producao_sim[dia-1]
        
        # Calcular exigências (simplificado)
        nel = 0.08 * (dados_vaca['peso_vivo'] ** 0.75) + prod * (0.0929 * dados_vaca['gordura_leite'] + 0.0547 * dados_vaca['proteina_leite'] + 0.192)
        pm = 3.8 * ((dados_vaca['peso_vivo'] / 1000) ** 0.75) + prod * (dados_vaca['proteina_leite'] * 10) * 1.25
        ca = dados_vaca['peso_vivo'] * 0.0154 + prod * 1.22
        
        nel_sim.append(nel)
        pm_sim.append(pm)
        ca_sim.append(ca)
    
    fig2, (ax2, ax3, ax4) = plt.subplots(3, 1, figsize=(10, 12), sharex=True)
    
    # Gráfico de NEL
    ax2.plot(dias, nel_sim, 'r-', linewidth=2)
    ax2.set_ylabel('NEL (Mcal/dia)')
    ax2.set_title('Exigência de Energia ao Longo da Lactação')
    ax2.grid(True)
    
    # Gráfico de PM
    ax3.plot(dias, pm_sim, 'g-', linewidth=2)
    ax3.set_ylabel('PM (g/dia)')
    ax3.set_title('Exigência de Proteína ao Longo da Lactação')
    ax3.grid(True)
    
    # Gráfico de Cálcio
    ax4.plot(dias, ca_sim, 'b-', linewidth=2)
    ax4.set_ylabel('Cálcio (g/dia)')
    ax4.set_xlabel('Dias de Lactação')
    ax4.set_title('Exigência de Cálcio ao Longo da Lactação')
    ax4.grid(True)
    
    plt.tight_layout()
    
    canvas2 = FigureCanvasTkAgg(fig2, master=aba_linha)
    canvas2.draw()
    canvas2.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    # Botão para salvar gráficos
    def salvar_graficos():
        nome_arquivo = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png")],
            title="Salvar Gráficos"
        )
        if nome_arquivo:
            fig1.savefig(nome_arquivo.replace('.png', '_barras.png'))
            fig2.savefig(nome_arquivo.replace('.png', '_linha.png'))
            messagebox.showinfo("Sucesso", f"Gráficos salvos em: {nome_arquivo}")
    
    btn_salvar = ttk.Button(janela_graficos, text="Salvar Gráficos", command=salvar_graficos)
    btn_salvar.pack(pady=10)

# Classe principal da aplicação
class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Calculadora de Exigências Nutricionais - NRC 2001")
        self.root.geometry("600x700")
        
        # Frame principal
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Labels e Entradas
        labels = [
            ("Peso Vivo (kg):", 0), ("Produção de Leite (kg/dia):", 1),
            ("Gordura no Leite (%):", 2), ("Proteína no Leite (%):", 3),
            ("Dias de Lactação:", 4), ("Número de Lactações:", 5),
            ("Peso ao Parto (kg):", 6), ("Dias de Gestação (opcional):", 7),
            ("Temperatura (°C):", 8), ("Umidade (%):", 9)
        ]
        
        self.entries = {}
        for i, (text, row) in enumerate(labels):
            ttk.Label(main_frame, text=text).grid(row=row, column=0, sticky=tk.W, pady=2)
            entry = ttk.Entry(main_frame, width=15)
            entry.grid(row=row, column=1, sticky=tk.W, pady=2)
            self.entries[text] = entry
        
        # Raça
        ttk.Label(main_frame, text="Raça:").grid(row=10, column=0, sticky=tk.W, pady=2)
        self.raca_var = tk.StringVar()
        raca_combo = ttk.Combobox(main_frame, textvariable=self.raca_var, width=12)
        raca_combo['values'] = ('Holandesa', 'Jersey', 'Gir', 'Girolando')
        raca_combo.grid(row=10, column=1, sticky=tk.W, pady=2)
        raca_combo.current(0)
        
        # Manejo
        ttk.Label(main_frame, text="Manejo:").grid(row=11, column=0, sticky=tk.W, pady=2)
        self.manejo_var = tk.StringVar()
        manejo_combo = ttk.Combobox(main_frame, textvariable=self.manejo_var, width=12)
        manejo_combo['values'] = ('Confinamento', 'Pasto', 'Semi-confinamento')
        manejo_combo.grid(row=11, column=1, sticky=tk.W, pady=2)
        manejo_combo.current(0)
        
        # Botões
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=12, column=0, columnspan=2, pady=10)
        
        calcular_btn = ttk.Button(btn_frame, text="Calcular", command=self.calcular)
        calcular_btn.grid(row=0, column=0, padx=5)
        
        self.btn_pdf = ttk.Button(btn_frame, text="Gerar Relatório PDF", command=self.gerar_pdf, state=tk.DISABLED)
        self.btn_pdf.grid(row=0, column=1, padx=5)
        
        self.btn_graficos = ttk.Button(btn_frame, text="Exibir Gráficos", command=self.exibir_graficos, state=tk.DISABLED)
        self.btn_graficos.grid(row=0, column=2, padx=5)
        
        # Área de Resultados
        self.result_text = tk.Text(main_frame, width=70, height=20)
        self.result_text.grid(row=13, column=0, columnspan=2, pady=10)
        
        # Armazenar resultados e dados
        self.resultados = None
        self.dados_vaca = None
        
    def calcular(self):
        try:
            # Obter valores das entradas
            peso_vivo = float(self.entries["Peso Vivo (kg):"].get())
            producao_leite = float(self.entries["Produção de Leite (kg/dia):"].get())
            gordura_leite = float(self.entries["Gordura no Leite (%):"].get())
            proteina_leite = float(self.entries["Proteína no Leite (%):"].get())
            dias_lactacao = int(self.entries["Dias de Lactação:"].get())
            num_lactacoes = int(self.entries["Número de Lactações:"].get())
            peso_parto = float(self.entries["Peso ao Parto (kg):"].get())
            dias_gestacao_str = self.entries["Dias de Gestação (opcional):"].get()
            dias_gestacao = int(dias_gestacao_str) if dias_gestacao_str else None
            temperatura = float(self.entries["Temperatura (°C):"].get())
            umidade = float(self.entries["Umidade (%):"].get())
            raca = self.raca_var.get()
            manejo = self.manejo_var.get()
            
            # Armazenar dados da vaca
            self.dados_vaca = {
                "peso_vivo": peso_vivo,
                "producao_leite": producao_leite,
                "gordura_leite": gordura_leite,
                "proteina_leite": proteina_leite,
                "dias_lactacao": dias_lactacao,
                "num_lactacoes": num_lactacoes,
                "peso_parto": peso_parto,
                "dias_gestacao": dias_gestacao,
                "temperatura": temperatura,
                "umidade": umidade,
                "raca": raca,
                "manejo": manejo
            }
            
            # Calcular exigências
            self.resultados = calcular_exigencias_nrc_expandido(
                peso_vivo, producao_leite, gordura_leite, proteina_leite,
                dias_lactacao, num_lactacoes, peso_parto, dias_gestacao,
                raca, temperatura, umidade, manejo
            )
            
            # Exibir resultados
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "Exigências Nutricionais Diárias:\n\n")
            for nutriente, valor in self.resultados.items():
                self.result_text.insert(tk.END, f"{nutriente}: {valor}\n")
                
            # Habilitar botões de PDF e Gráficos
            self.btn_pdf.config(state=tk.NORMAL)
            self.btn_graficos.config(state=tk.NORMAL)
                
        except ValueError:
            messagebox.showerror("Erro", "Por favor, preencha todos os campos com valores numéricos válidos.")
    
    def gerar_pdf(self):
        if self.resultados and self.dados_vaca:
            gerar_relatorio_pdf(self.resultados, self.dados_vaca)
    
    def exibir_graficos(self):
        if self.resultados and self.dados_vaca:
            criar_graficos(self.resultados, self.dados_vaca, self.root)

# Executar a aplicação
if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()