import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import math
import sqlite3
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

# Função principal de cálculo de exigências nutricionais
def calcular_exigencias_nrc_expandido(peso_vivo, producao_leite, gordura_leite, proteina_leite, 
                                     dias_lactacao, num_lactacoes, peso_parto, dias_gestacao,
                                     raca, temperatura, umidade, manejo):
    """
    Calcula exigências nutricionais expandidas para vacas leiteiras (NRC 2001 e outros).
    Considera raça, condições ambientais e manejo.
    """
    # Fatores de ajuste
    fator_raca = {"Holandesa": 1.0, "Jersey": 1.1, "Gir": 1.05, "Girolando": 1.02}
    fator_raca_nel_pm = fator_raca.get(raca, 1.0)
    
    # Índice de Temperatura e Umidade (ITH)
    ith = (0.72 * (temperatura + umidade) + 40.6)
    fator_estresse_calor = 1.0
    if ith > 72:
        # Aumentar NEL em 5% para cada 10 unidades acima de 72
        fator_estresse_calor_nel = 1.0 + (0.05 * ((ith - 72) / 10))
        # Aumentar K em 1% e Na em 0.5% para cada 10 unidades acima de 72
        fator_estresse_calor_k = 1.0 + (0.01 * ((ith - 72) / 10))
        fator_estresse_calor_na = 1.0 + (0.005 * ((ith - 72) / 10))
    else:
        fator_estresse_calor_nel = 1.0
        fator_estresse_calor_k = 1.0
        fator_estresse_calor_na = 1.0
    
    # Fator de manejo (atividade)
    fator_manejo = {"Confinamento": 1.0, "Pasto": 1.1, "Semi-confinamento": 1.05}
    fator_atividade = fator_manejo.get(manejo, 1.0)
    
    # --- Cálculo de NEL (Energia Líquida de Lactação) ---
    NEL_mant = 0.08 * (peso_vivo ** 0.75) * fator_atividade
    NEL_lact = producao_leite * (0.0929 * gordura_leite + 0.0547 * proteina_leite + 0.192)
    NEL_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        t = dias_gestacao - 190
        NEL_gest = 0.00318 * peso_parto * math.exp(0.018 * t)
    NEL_total = (NEL_mant + NEL_lact + NEL_gest) * fator_raca_nel_pm * fator_estresse_calor_nel
    
    # --- Cálculo de PM (Proteína Metabolizável) ---
    # Ajuste no cálculo de proteína para refletir melhor as exigências
    PM_mant = 3.8 * ((peso_vivo / 1000) ** 0.75)
    PM_lact = producao_leite * (proteina_leite * 10) * 1.25  # 1.25 = fator de eficiência
    PM_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        PM_gest = 1.136 * ((peso_parto / 1000) ** 0.7) * math.exp(-0.00203 * (dias_gestacao - 190))
    PM_total = (PM_mant + PM_lact + PM_gest) * fator_raca_nel_pm
    
    # --- Cálculo de Proteína Bruta (PB) a partir da PM ---
    # Conversão de PM para PB (fator de eficiência de 0.65)
    PB_total = PM_total / 0.65
    
    # --- Cálculo de Matéria Seca (MS) ---
    # Exigência de MS baseada no consumo voluntário (3-4% do PV)
    MS_total = peso_vivo * 0.035  # 3.5% do peso vivo
    
    # --- Minerais (Cálcio e Fósforo) ---
    Ca_mant = peso_vivo * 0.0154
    Ca_lact = producao_leite * 1.22
    Ca_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        Ca_gest = 0.0076 * peso_parto * math.exp(0.014 * (dias_gestacao - 190))
    Ca_total = Ca_mant + Ca_lact + Ca_gest
    
    P_mant = peso_vivo * 0.014
    P_lact = producao_leite * 0.9
    P_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        P_gest = 0.0047 * peso_parto * math.exp(0.011 * (dias_gestacao - 190))
    P_total = P_mant + P_lact + P_gest
    
    # --- Vitaminas ---
    VitA_mant = 110 * peso_vivo
    VitA_lact = 30 * producao_leite
    VitA_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        # Peso fetal estimado: 0.028 * (dias_gestacao - 190) ** 2
        peso_fetal = 0.028 * ((dias_gestacao - 190) ** 2)
        VitA_gest = 40 * peso_fetal
    VitA_total = VitA_mant + VitA_lact + VitA_gest
    
    VitD_mant = 30 * peso_vivo
    VitD_lact = 15 * producao_leite
    VitD_total = VitD_mant + VitD_lact
    
    VitE_mant = 1.5 * peso_vivo
    VitE_lact = 0.5 * producao_leite
    VitE_total = VitE_mant + VitE_lact
    
    # --- Outros Minerais ---
    Mg_mant = 0.004 * peso_vivo
    Mg_lact = 0.12 * producao_leite
    Mg_total = Mg_mant + Mg_lact
    
    K_mant = 0.039 * peso_vivo
    K_lact = 1.5 * producao_leite
    K_total = (K_mant + K_lact) * fator_estresse_calor_k
    
    Na_mant = 0.015 * peso_vivo
    Na_lact = 1.0 * producao_leite
    Na_total = (Na_mant + Na_lact) * fator_estresse_calor_na
    
    Cl_mant = 0.022 * peso_vivo
    Cl_lact = 1.2 * producao_leite
    Cl_total = Cl_mant + Cl_lact
    
    S_mant = 0.0006 * peso_vivo
    S_lact = 0.3 * producao_leite
    S_total = S_mant + S_lact
    
    Cu_mant = 0.1 * peso_vivo
    Cu_lact = 0.5 * producao_leite
    Cu_total = Cu_mant + Cu_lact
    
    Zn_mant = 0.5 * peso_vivo
    Zn_lact = 15 * producao_leite
    Zn_total = Zn_mant + Zn_lact
    
    Se_mant = 0.0006 * peso_vivo
    Se_lact = 0.05 * producao_leite
    Se_total = Se_mant + Se_lact
    
    I_mant = 0.006 * peso_vivo
    I_lact = 0.1 * producao_leite
    I_total = I_mant + I_lact
    
    Co_mant = 0.0001 * peso_vivo
    Co_lact = 0.01 * producao_leite
    Co_total = Co_mant + Co_lact
    
    Mn_mant = 0.002 * peso_vivo
    Mn_lact = 0.2 * producao_leite
    Mn_total = Mn_mant + Mn_lact
    
    # --- FDN e CNF ---
    # FDN mínimo: 1.2% do PV, mas pode ser ajustado pela produção
    FDN_min = 0.012 * peso_vivo  # kg/dia
    # CNF: não há exigência, mas pode ser estimado como 35-40% da MS
    
    return {
        "Energia (NEL, Mcal/dia)": round(NEL_total, 2),
        "Proteína Metabolizável (PM, g/dia)": round(PM_total, 0),
        "Proteína Bruta (PB, g/dia)": round(PB_total, 0),
        "Matéria Seca (MS, kg/dia)": round(MS_total, 1),
        "Cálcio (g/dia)": round(Ca_total, 1),
        "Fósforo (g/dia)": round(P_total, 1),
        "Magnésio (g/dia)": round(Mg_total, 1),
        "Potássio (g/dia)": round(K_total, 1),
        "Sódio (g/dia)": round(Na_total, 1),
        "Cloro (g/dia)": round(Cl_total, 1),
        "Enxofre (g/dia)": round(S_total, 1),
        "Cobre (mg/dia)": round(Cu_total, 0),
        "Zinco (mg/dia)": round(Zn_total, 0),
        "Selênio (mg/dia)": round(Se_total, 2),
        "Iodo (mg/dia)": round(I_total, 2),
        "Cobalto (mg/dia)": round(Co_total, 3),
        "Manganês (mg/dia)": round(Mn_total, 0),
        "Vitamina A (UI/dia)": round(VitA_total, 0),
        "Vitamina D (UI/dia)": round(VitD_total, 0),
        "Vitamina E (UI/dia)": round(VitE_total, 0),
        "FDN mínimo (kg/dia)": round(FDN_min, 1)
    }

# Função para gerar recomendações de dieta
def gerar_recomendacoes(resultados, dados_vaca):
    recomendacoes = []
    
    # Recomendações básicas de dieta
    recomendacoes.append("=== RECOMENDAÇÕES DE DIETA ===")
    recomendacoes.append(f"Com base nas exigências calculadas para a vaca de {dados_vaca['peso_vivo']} kg:")
    
    # Matéria Seca
    ms = resultados["Matéria Seca (MS, kg/dia)"]
    recomendacoes.append(f"\n1. Consumo de Matéria Seca: {ms} kg/dia")
    recomendacoes.append("   - Distribuir em 2-3 refeições diárias")
    
    # Energia
    nel = resultados["Energia (NEL, Mcal/dia)"]
    recomendacoes.append(f"\n2. Energia (NEL): {nel} Mcal/dia")
    recomendacoes.append("   - Fontes recomendadas: grãos (milho, sorgo), subprodutos (polpa cítrica)")
    
    # Proteína
    pb = resultados["Proteína Bruta (PB, g/dia)"]
    recomendacoes.append(f"\n3. Proteína Bruta: {pb} g/dia")
    recomendacoes.append("   - Fontes recomendadas: farelo de soja, farelo de algodão, ureia (até 1% da MS)")
    
    # Minerais principais
    ca = resultados["Cálcio (g/dia)"]
    p = resultados["Fósforo (g/dia)"]
    recomendacoes.append(f"\n4. Minerais Principais:")
    recomendacoes.append(f"   - Cálcio: {ca} g/dia (fonte: calcário)")
    recomendacoes.append(f"   - Fósforo: {p} g/dia (fonte: fosfato bicálcico)")
    
    # Vitaminas
    vitA = resultados["Vitamina A (UI/dia)"]
    vitD = resultados["Vitamina D (UI/dia)"]
    vitE = resultados["Vitamina E (UI/dia)"]
    recomendacoes.append(f"\n5. Vitaminas:")
    recomendacoes.append(f"   - Vitamina A: {vitA} UI/dia")
    recomendacoes.append(f"   - Vitamina D: {vitD} UI/dia")
    recomendacoes.append(f"   - Vitamina E: {vitE} UI/dia")
    
    # FDN
    fdn = resultados["FDN mínimo (kg/dia)"]
    recomendacoes.append(f"\n6. Fibra (FDN): mínimo de {fdn} kg/dia")
    recomendacoes.append("   - Fontes recomendadas: silagem de milho, capim-elefante, feno")
    
    # Observações adicionais
    recomendacoes.append("\n=== OBSERVAÇÕES ===")
    if dados_vaca['dias_lactacao'] < 100:
        recomendacoes.append("- Vacas no início da lactação requerem maior densidade nutricional")
    if dados_vaca['producao_leite'] > 30:
        recomendacoes.append("- Alta produção: aumentar concentrado e monitorar saúde ruminal")
    if dados_vaca['dias_gestacao'] and dados_vaca['dias_gestacao'] > 200:
        recomendacoes.append("- Gestação avançada: ajustar minerais e energia")
    
    return "\n".join(recomendacoes)

# Função para gerar relatório PDF
def gerar_relatorio_pdf(resultados, dados_vaca, recomendacoes, nome_arquivo=None):
    if not nome_arquivo:
        nome_arquivo = filedialog.asksaveasfilename(
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf")],
            title="Salvar Relatório"
        )
        if not nome_arquivo:
            return
    
    doc = SimpleDocTemplate(nome_arquivo, pagesize=letter)
    elements = []
    styles = getSampleStyleSheet()
    
    # Título
    elements.append(Paragraph("Relatório de Exigências Nutricionais - NRC 2001", styles['Title']))
    
    # Dados da vaca
    dados_texto = f"""
    <b>Dados da Vaca:</b><br/>
    Peso Vivo: {dados_vaca['peso_vivo']} kg<br/>
    Produção de Leite: {dados_vaca['producao_leite']} kg/dia<br/>
    Gordura no Leite: {dados_vaca['gordura_leite']}%<br/>
    Proteína no Leite: {dados_vaca['proteina_leite']}%<br/>
    Dias de Lactação: {dados_vaca['dias_lactacao']}<br/>
    Raça: {dados_vaca['raca']}<br/>
    Manejo: {dados_vaca['manejo']}<br/>
    Temperatura: {dados_vaca['temperatura']}°C<br/>
    Umidade: {dados_vaca['umidade']}%<br/>
    """
    elements.append(Paragraph(dados_texto, styles['Normal']))
    
    # Tabela de resultados
    data = [["Nutriente", "Exigência Diária"]]
    for nutriente, valor in resultados.items():
        data.append([nutriente, str(valor)])
    
    t = Table(data)
    t.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(t)
    
    # Recomendações
    elements.append(Paragraph("<br/><b>Recomendações de Dieta:</b>", styles['Heading2']))
    for linha in recomendacoes.split('\n'):
        elements.append(Paragraph(linha, styles['Normal']))
    
    doc.build(elements)
    messagebox.showinfo("Sucesso", f"Relatório salvo em: {nome_arquivo}")

# Função para criar gráficos
def criar_graficos(resultados, dados_vaca, root):
    # Criar janela para gráficos
    janela_graficos = tk.Toplevel(root)
    janela_graficos.title("Gráficos de Exigências Nutricionais")
    janela_graficos.geometry("1000x700")
    
    # Criar notebook para abas
    notebook = ttk.Notebook(janela_graficos)
    notebook.pack(fill='both', expand=True)
    
    # Aba 1: Gráfico de barras (exigências atuais)
    aba_barras = ttk.Frame(notebook)
    notebook.add(aba_barras, text="Exigências Atuais")
    
    # Selecionar nutrientes principais para o gráfico
    nutrientes_principais = {
        "Energia (NEL, Mcal/dia)": resultados["Energia (NEL, Mcal/dia)"],
        "Proteína Bruta (PB, kg/dia)": resultados["Proteína Bruta (PB, g/dia)"] / 1000,  # Converter para kg
        "Matéria Seca (MS, kg/dia)": resultados["Matéria Seca (MS, kg/dia)"],
        "Cálcio (g/dia)": resultados["Cálcio (g/dia)"],
        "Fósforo (g/dia)": resultados["Fósforo (g/dia)"],
        "Magnésio (g/dia)": resultados["Magnésio (g/dia)"]
    }
    
    fig1, ax1 = plt.subplots(figsize=(10, 6))
    nutrientes = list(nutrientes_principais.keys())
    valores = list(nutrientes_principais.values())
    
    bars = ax1.bar(nutrientes, valores, color='skyblue')
    ax1.set_ylabel('Exigência Diária')
    ax1.set_title('Exigências Nutricionais Principais')
    plt.xticks(rotation=45, ha='right')
    
    # Adicionar valores nas barras
    for bar in bars:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.2f}', ha='center', va='bottom')
    
    canvas1 = FigureCanvasTkAgg(fig1, master=aba_barras)
    canvas1.draw()
    canvas1.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    # Aba 2: Gráfico de linha (exigências ao longo do tempo)
    aba_linha = ttk.Frame(notebook)
    notebook.add(aba_linha, text="Exigências ao Longo do Tempo")
    
    # Simular exigências ao longo da lactação (305 dias)
    dias = np.arange(1, 306)
    
    # Simular variação de produção de leite (curva típica de lactação)
    producao_sim = dados_vaca['producao_leite'] * np.exp(-0.05 * (dias - 60)**2 / 1000)
    
    # Calcular exigências para cada dia
    nel_sim = []
    pb_sim = []
    ca_sim = []
    
    for dia in dias:
        # Atualizar produção de leite simulada
        prod = producao_sim[dia-1]
        
        # Calcular exigências (simplificado)
        nel = 0.08 * (dados_vaca['peso_vivo'] ** 0.75) + prod * (0.0929 * dados_vaca['gordura_leite'] + 0.0547 * dados_vaca['proteina_leite'] + 0.192)
        pm = 3.8 * ((dados_vaca['peso_vivo'] / 1000) ** 0.75) + prod * (dados_vaca['proteina_leite'] * 10) * 1.25
        pb = pm / 0.65  # Converter PM para PB
        ca = dados_vaca['peso_vivo'] * 0.0154 + prod * 1.22
        
        nel_sim.append(nel)
        pb_sim.append(pb)
        ca_sim.append(ca)
    
    fig2, (ax2, ax3, ax4) = plt.subplots(3, 1, figsize=(10, 12), sharex=True)
    
    # Gráfico de NEL
    ax2.plot(dias, nel_sim, 'r-', linewidth=2)
    ax2.set_ylabel('NEL (Mcal/dia)')
    ax2.set_title('Exigência de Energia ao Longo da Lactação')
    ax2.grid(True)
    
    # Gráfico de PB
    ax3.plot(dias, pb_sim, 'g-', linewidth=2)
    ax3.set_ylabel('PB (g/dia)')
    ax3.set_title('Exigência de Proteína Bruta ao Longo da Lactação')
    ax3.grid(True)
    
    # Gráfico de Cálcio
    ax4.plot(dias, ca_sim, 'b-', linewidth=2)
    ax4.set_ylabel('Cálcio (g/dia)')
    ax4.set_xlabel('Dias de Lactação')
    ax4.set_title('Exigência de Cálcio ao Longo da Lactação')
    ax4.grid(True)
    
    plt.tight_layout()
    
    canvas2 = FigureCanvasTkAgg(fig2, master=aba_linha)
    canvas2.draw()
    canvas2.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    # Botão para salvar gráficos
    def salvar_graficos():
        nome_arquivo = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png")],
            title="Salvar Gráficos"
        )
        if nome_arquivo:
            fig1.savefig(nome_arquivo.replace('.png', '_barras.png'))
            fig2.savefig(nome_arquivo.replace('.png', '_linha.png'))
            messagebox.showinfo("Sucesso", f"Gráficos salvos em: {nome_arquivo}")
    
    btn_salvar = ttk.Button(janela_graficos, text="Salvar Gráficos", command=salvar_graficos)
    btn_salvar.pack(pady=10)

# Função para inicializar o banco de dados
def init_db():
    conn = sqlite3.connect('exigencias_vacas.db')
    cursor = conn.cursor()
    
    # Criar tabela de vacas
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS vacas (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nome TEXT,
        peso_vivo REAL,
        producao_leite REAL,
        gordura_leite REAL,
        proteina_leite REAL,
        dias_lactacao INTEGER,
        num_lactacoes INTEGER,
        peso_parto REAL,
        dias_gestacao INTEGER,
        raca TEXT,
        temperatura REAL,
        umidade REAL,
        manejo TEXT,
        data_cadastro TEXT
    )
    ''')
    
    # Criar tabela de resultados
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS resultados (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        id_vaca INTEGER,
        nutriente TEXT,
        valor REAL,
        data_calculo TEXT,
        FOREIGN KEY (id_vaca) REFERENCES vacas (id)
    )
    ''')
    
    conn.commit()
    conn.close()

# Classe principal da aplicação
class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Calculadora de Exigências Nutricionais - NRC 2001")
        self.root.geometry("800x700")
        
        # Inicializar banco de dados
        init_db()
        
        # Frame principal
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Notebook para abas
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Aba 1: Cálculo Individual
        self.aba_individual = ttk.Frame(self.notebook)
        self.notebook.add(self.aba_individual, text="Cálculo Individual")
        
        # Aba 2: Múltiplas Vacas
        self.aba_multiplas = ttk.Frame(self.notebook)
        self.notebook.add(self.aba_multiplas, text="Múltiplas Vacas")
        
        # Aba 3: Histórico
        self.aba_historico = ttk.Frame(self.notebook)
        self.notebook.add(self.aba_historico, text="Histórico")
        
        # Configurar aba individual
        self.configurar_aba_individual()
        
        # Configurar aba múltiplas vacas
        self.configurar_aba_multiplas()
        
        # Configurar aba histórico
        self.configurar_aba_historico()
        
        # Armazenar resultados e dados
        self.resultados = None
        self.dados_vaca = None
        
    def configurar_aba_individual(self):
        # Labels e Entradas
        labels = [
            ("Nome da Vaca:", 0), ("Peso Vivo (kg):", 1), ("Produção de Leite (kg/dia):", 2),
            ("Gordura no Leite (%):", 3), ("Proteína no Leite (%):", 4),
            ("Dias de Lactação:", 5), ("Número de Lactações:", 6),
            ("Peso ao Parto (kg):", 7), ("Dias de Gestação (opcional):", 8),
            ("Temperatura (°C):", 9), ("Umidade (%):", 10)
        ]
        
        self.entries = {}
        for i, (text, row) in enumerate(labels):
            ttk.Label(self.aba_individual, text=text).grid(row=row, column=0, sticky=tk.W, pady=2)
            entry = ttk.Entry(self.aba_individual, width=15)
            entry.grid(row=row, column=1, sticky=tk.W, pady=2)
            self.entries[text] = entry
        
        # Raça
        ttk.Label(self.aba_individual, text="Raça:").grid(row=11, column=0, sticky=tk.W, pady=2)
        self.raca_var = tk.StringVar()
        raca_combo = ttk.Combobox(self.aba_individual, textvariable=self.raca_var, width=12)
        raca_combo['values'] = ('Holandesa', 'Jersey', 'Gir', 'Girolando')
        raca_combo.grid(row=11, column=1, sticky=tk.W, pady=2)
        raca_combo.current(0)
        
        # Manejo
        ttk.Label(self.aba_individual, text="Manejo:").grid(row=12, column=0, sticky=tk.W, pady=2)
        self.manejo_var = tk.StringVar()
        manejo_combo = ttk.Combobox(self.aba_individual, textvariable=self.manejo_var, width=12)
        manejo_combo['values'] = ('Confinamento', 'Pasto', 'Semi-confinamento')
        manejo_combo.grid(row=12, column=1, sticky=tk.W, pady=2)
        manejo_combo.current(0)
        
        # Botões
        btn_frame = ttk.Frame(self.aba_individual)
        btn_frame.grid(row=13, column=0, columnspan=2, pady=10)
        
        calcular_btn = ttk.Button(btn_frame, text="Calcular", command=self.calcular_individual)
        calcular_btn.grid(row=0, column=0, padx=5)
        
        self.btn_pdf = ttk.Button(btn_frame, text="Gerar Relatório PDF", command=self.gerar_pdf_individual, state=tk.DISABLED)
        self.btn_pdf.grid(row=0, column=1, padx=5)
        
        self.btn_graficos = ttk.Button(btn_frame, text="Exibir Gráficos", command=self.exibir_graficos_individual, state=tk.DISABLED)
        self.btn_graficos.grid(row=0, column=2, padx=5)
        
        # Área de Resultados
        self.result_text = tk.Text(self.aba_individual, width=70, height=15)
        self.result_text.grid(row=14, column=0, columnspan=2, pady=10)
        
        # Área de Recomendações
        ttk.Label(self.aba_individual, text="Recomendações de Dieta:").grid(row=15, column=0, sticky=tk.W, pady=2)
        self.recomendacoes_text = tk.Text(self.aba_individual, width=70, height=10)
        self.recomendacoes_text.grid(row=16, column=0, columnspan=2, pady=10)
        
    def configurar_aba_multiplas(self):
        # Frame para adicionar vacas
        add_frame = ttk.LabelFrame(self.aba_multiplas, text="Adicionar Vaca", padding="10")
        add_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=10)
        
        # Campos para adicionar vaca
        ttk.Label(add_frame, text="Nome:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.nome_entry = ttk.Entry(add_frame, width=15)
        self.nome_entry.grid(row=0, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(add_frame, text="Peso Vivo (kg):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.peso_entry = ttk.Entry(add_frame, width=15)
        self.peso_entry.grid(row=1, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(add_frame, text="Produção (kg/dia):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.producao_entry = ttk.Entry(add_frame, width=15)
        self.producao_entry.grid(row=2, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(add_frame, text="Gordura (%):").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.gordura_entry = ttk.Entry(add_frame, width=15)
        self.gordura_entry.grid(row=3, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(add_frame, text="Proteína (%):").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.proteina_entry = ttk.Entry(add_frame, width=15)
        self.proteina_entry.grid(row=4, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(add_frame, text="Dias Lactação:").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.dias_lact_entry = ttk.Entry(add_frame, width=15)
        self.dias_lact_entry.grid(row=5, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(add_frame, text="Raça:").grid(row=6, column=0, sticky=tk.W, pady=2)
        self.raca_multi_var = tk.StringVar()
        raca_multi_combo = ttk.Combobox(add_frame, textvariable=self.raca_multi_var, width=12)
        raca_multi_combo['values'] = ('Holandesa', 'Jersey', 'Gir', 'Girolando')
        raca_multi_combo.grid(row=6, column=1, sticky=tk.W, pady=2)
        raca_multi_combo.current(0)
        
        ttk.Button(add_frame, text="Adicionar Vaca", command=self.adicionar_vaca).grid(row=7, column=0, columnspan=2, pady=10)
        
        # Frame para lista de vacas
        list_frame = ttk.LabelFrame(self.aba_multiplas, text="Vacas Cadastradas", padding="10")
        list_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        
        # Treeview para lista de vacas
        self.tree_vacas = ttk.Treeview(list_frame, columns=('id', 'nome', 'peso', 'producao', 'gordura', 'proteina', 'dias_lact', 'raca'), show='headings')
        self.tree_vacas.heading('id', text='ID')
        self.tree_vacas.heading('nome', text='Nome')
        self.tree_vacas.heading('peso', text='Peso (kg)')
        self.tree_vacas.heading('producao', text='Produção (kg/dia)')
        self.tree_vacas.heading('gordura', text='Gordura (%)')
        self.tree_vacas.heading('proteina', text='Proteína (%)')
        self.tree_vacas.heading('dias_lact', text='Dias Lact.')
        self.tree_vacas.heading('raca', text='Raça')
        
        self.tree_vacas.column('id', width=30)
        self.tree_vacas.column('nome', width=100)
        self.tree_vacas.column('peso', width=80)
        self.tree_vacas.column('producao', width=80)
        self.tree_vacas.column('gordura', width=70)
        self.tree_vacas.column('proteina', width=70)
        self.tree_vacas.column('dias_lact', width=70)
        self.tree_vacas.column('raca', width=80)
        
        self.tree_vacas.pack(fill=tk.BOTH, expand=True)
        
        # Botões para ações
        btn_frame = ttk.Frame(list_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_frame, text="Calcular Todas", command=self.calcular_todas).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Comparar", command=self.comparar_vacas).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Remover", command=self.remover_vaca).pack(side=tk.LEFT, padx=5)
        
        # Área para resultados
        self.result_multi_text = tk.Text(self.aba_multiplas, width=70, height=10)
        self.result_multi_text.grid(row=1, column=0, columnspan=2, pady=10, padx=10)
        
        # Atualizar lista de vacas
        self.atualizar_lista_vacas()
        
    def configurar_aba_historico(self):
        # Treeview para histórico
        self.tree_historico = ttk.Treeview(self.aba_historico, columns=('id', 'vaca', 'nutriente', 'valor', 'data'), show='headings')
        self.tree_historico.heading('id', text='ID')
        self.tree_historico.heading('vaca', text='Vaca')
        self.tree_historico.heading('nutriente', text='Nutriente')
        self.tree_historico.heading('valor', text='Valor')
        self.tree_historico.heading('data', text='Data')
        
        self.tree_historico.column('id', width=30)
        self.tree_historico.column('vaca', width=100)
        self.tree_historico.column('nutriente', width=200)
        self.tree_historico.column('valor', width=100)
        self.tree_historico.column('data', width=100)
        
        self.tree_historico.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Botões
        btn_frame = ttk.Frame(self.aba_historico)
        btn_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(btn_frame, text="Atualizar", command=self.atualizar_historico).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Limpar Histórico", command=self.limpar_historico).pack(side=tk.LEFT, padx=5)
        
        # Atualizar histórico
        self.atualizar_historico()
        
    def calcular_individual(self):
        try:
            # Obter valores das entradas
            nome = self.entries["Nome da Vaca:"].get() or "Vaca sem nome"
            peso_vivo = float(self.entries["Peso Vivo (kg):"].get())
            producao_leite = float(self.entries["Produção de Leite (kg/dia):"].get())
            gordura_leite = float(self.entries["Gordura no Leite (%):"].get())
            proteina_leite = float(self.entries["Proteína no Leite (%):"].get())
            dias_lactacao = int(self.entries["Dias de Lactação:"].get())
            num_lactacoes = int(self.entries["Número de Lactações:"].get())
            peso_parto = float(self.entries["Peso ao Parto (kg):"].get())
            dias_gestacao_str = self.entries["Dias de Gestação (opcional):"].get()
            dias_gestacao = int(dias_gestacao_str) if dias_gestacao_str else None
            temperatura = float(self.entries["Temperatura (°C):"].get())
            umidade = float(self.entries["Umidade (%):"].get())
            raca = self.raca_var.get()
            manejo = self.manejo_var.get()
            
            # Armazenar dados da vaca
            self.dados_vaca = {
                "nome": nome,
                "peso_vivo": peso_vivo,
                "producao_leite": producao_leite,
                "gordura_leite": gordura_leite,
                "proteina_leite": proteina_leite,
                "dias_lactacao": dias_lactacao,
                "num_lactacoes": num_lactacoes,
                "peso_parto": peso_parto,
                "dias_gestacao": dias_gestacao,
                "temperatura": temperatura,
                "umidade": umidade,
                "raca": raca,
                "manejo": manejo
            }
            
            # Calcular exigências
            self.resultados = calcular_exigencias_nrc_expandido(
                peso_vivo, producao_leite, gordura_leite, proteina_leite,
                dias_lactacao, num_lactacoes, peso_parto, dias_gestacao,
                raca, temperatura, umidade, manejo
            )
            
            # Salvar no banco de dados
            self.salvar_resultados_bd(nome, self.dados_vaca, self.resultados)
            
            # Gerar recomendações
            recomendacoes = gerar_recomendacoes(self.resultados, self.dados_vaca)
            
            # Exibir resultados
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"Exigências Nutricionais Diárias para {nome}:\n\n")
            for nutriente, valor in self.resultados.items():
                self.result_text.insert(tk.END, f"{nutriente}: {valor}\n")
                
            # Exibir recomendações
            self.recomendacoes_text.delete(1.0, tk.END)
            self.recomendacoes_text.insert(tk.END, recomendacoes)
                
            # Habilitar botões de PDF e Gráficos
            self.btn_pdf.config(state=tk.NORMAL)
            self.btn_graficos.config(state=tk.NORMAL)
                
        except ValueError:
            messagebox.showerror("Erro", "Por favor, preencha todos os campos com valores numéricos válidos.")
    
    def gerar_pdf_individual(self):
        if self.resultados and self.dados_vaca:
            recomendacoes = self.recomendacoes_text.get(1.0, tk.END)
            gerar_relatorio_pdf(self.resultados, self.dados_vaca, recomendacoes)
    
    def exibir_graficos_individual(self):
        if self.resultados and self.dados_vaca:
            criar_graficos(self.resultados, self.dados_vaca, self.root)
    
    def adicionar_vaca(self):
        try:
            nome = self.nome_entry.get()
            peso_vivo = float(self.peso_entry.get())
            producao_leite = float(self.producao_entry.get())
            gordura_leite = float(self.gordura_entry.get())
            proteina_leite = float(self.proteina_entry.get())
            dias_lactacao = int(self.dias_lact_entry.get())
            raca = self.raca_multi_var.get()
            
            # Conectar ao banco de dados
            conn = sqlite3.connect('exigencias_vacas.db')
            cursor = conn.cursor()
            
            # Inserir vaca
            cursor.execute('''
            INSERT INTO vacas (nome, peso_vivo, producao_leite, gordura_leite, proteina_leite, dias_lactacao, raca, data_cadastro)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (nome, peso_vivo, producao_leite, gordura_leite, proteina_leite, dias_lactacao, raca, datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
            
            conn.commit()
            conn.close()
            
            # Limpar campos
            self.nome_entry.delete(0, tk.END)
            self.peso_entry.delete(0, tk.END)
            self.producao_entry.delete(0, tk.END)
            self.gordura_entry.delete(0, tk.END)
            self.proteina_entry.delete(0, tk.END)
            self.dias_lact_entry.delete(0, tk.END)
            
            # Atualizar lista
            self.atualizar_lista_vacas()
            
            messagebox.showinfo("Sucesso", "Vaca adicionada com sucesso!")
            
        except ValueError:
            messagebox.showerror("Erro", "Por favor, preencha todos os campos com valores numéricos válidos.")
    
    def atualizar_lista_vacas(self):
        # Limpar treeview
        for item in self.tree_vacas.get_children():
            self.tree_vacas.delete(item)
        
        # Conectar ao banco de dados
        conn = sqlite3.connect('exigencias_vacas.db')
        cursor = conn.cursor()
        
        # Selecionar vacas
        cursor.execute('SELECT id, nome, peso_vivo, producao_leite, gordura_leite, proteina_leite, dias_lactacao, raca FROM vacas')
        vacas = cursor.fetchall()
        
        conn.close()
        
        # Adicionar vacas ao treeview
        for vaca in vacas:
            self.tree_vacas.insert('', tk.END, values=vaca)
    
    def remover_vaca(self):
        selected_item = self.tree_vacas.selection()
        if not selected_item:
            messagebox.showwarning("Aviso", "Selecione uma vaca para remover.")
            return
        
        item = self.tree_vacas.item(selected_item)
        vaca_id = item['values'][0]
        
        # Confirmar remoção
        if messagebox.askyesno("Confirmar", "Deseja realmente remover esta vaca?"):
            # Conectar ao banco de dados
            conn = sqlite3.connect('exigencias_vacas.db')
            cursor = conn.cursor()
            
            # Remover vaca
            cursor.execute('DELETE FROM vacas WHERE id = ?', (vaca_id,))
            cursor.execute('DELETE FROM resultados WHERE id_vaca = ?', (vaca_id,))
            
            conn.commit()
            conn.close()
            
            # Atualizar lista
            self.atualizar_lista_vacas()
            messagebox.showinfo("Sucesso", "Vaca removida com sucesso!")
    
    def calcular_todas(self):
        # Conectar ao banco de dados
        conn = sqlite3.connect('exigencias_vacas.db')
        cursor = conn.cursor()
        
        # Selecionar vacas
        cursor.execute('SELECT id, nome, peso_vivo, producao_leite, gordura_leite, proteina_leite, dias_lactacao, raca FROM vacas')
        vacas = cursor.fetchall()
        
        conn.close()
        
        if not vacas:
            messagebox.showwarning("Aviso", "Nenhuma vaca cadastrada.")
            return
        
        # Calcular exigências para todas as vacas
        resultados_todos = []
        
        for vaca in vacas:
            id_vaca, nome, peso_vivo, producao_leite, gordura_leite, proteina_leite, dias_lactacao, raca = vaca
            
            # Dados padrão para campos não informados
            dados_vaca = {
                "nome": nome,
                "peso_vivo": peso_vivo,
                "producao_leite": producao_leite,
                "gordura_leite": gordura_leite,
                "proteina_leite": proteina_leite,
                "dias_lactacao": dias_lactacao,
                "num_lactacoes": 2,
                "peso_parto": peso_vivo * 0.9,
                "dias_gestacao": None,
                "temperatura": 25,
                "umidade": 60,
                "raca": raca,
                "manejo": "Confinamento"
            }
            
            # Calcular exigências
            resultados = calcular_exigencias_nrc_expandido(
                dados_vaca["peso_vivo"], dados_vaca["producao_leite"], 
                dados_vaca["gordura_leite"], dados_vaca["proteina_leite"],
                dados_vaca["dias_lactacao"], dados_vaca["num_lactacoes"],
                dados_vaca["peso_parto"], dados_vaca["dias_gestacao"],
                dados_vaca["raca"], dados_vaca["temperatura"], 
                dados_vaca["umidade"], dados_vaca["manejo"]
            )
            
            # Salvar no banco de dados
            self.salvar_resultados_bd(nome, dados_vaca, resultados, id_vaca)
            
            # Adicionar aos resultados
            resultados_todos.append((nome, resultados))
        
        # Exibir resultados
        self.result_multi_text.delete(1.0, tk.END)
        self.result_multi_text.insert(tk.END, "Exigências Nutricionais para Todas as Vacas:\n\n")
        
        for nome, resultados in resultados_todos:
            self.result_multi_text.insert(tk.END, f"=== {nome} ===\n")
            for nutriente, valor in resultados.items():
                self.result_multi_text.insert(tk.END, f"{nutriente}: {valor}\n")
            self.result_multi_text.insert(tk.END, "\n")
        
        messagebox.showinfo("Sucesso", "Cálculo realizado para todas as vacas!")
    
    def comparar_vacas(self):
        selected_items = self.tree_vacas.selection()
        if len(selected_items) < 2:
            messagebox.showwarning("Aviso", "Selecione pelo menos duas vacas para comparar.")
            return
        
        # Obter dados das vacas selecionadas
        vacas_selecionadas = []
        for item in selected_items:
            vaca_data = self.tree_vacas.item(item)['values']
            vacas_selecionadas.append(vaca_data)
        
        # Conectar ao banco de dados
        conn = sqlite3.connect('exigencias_vacas.db')
        cursor = conn.cursor()
        
        # Obter resultados das vacas
        resultados_comparacao = []
        for vaca in vacas_selecionadas:
            id_vaca = vaca[0]
            cursor.execute('SELECT nutriente, valor FROM resultados WHERE id_vaca = ? ORDER BY nutriente', (id_vaca,))
            resultados = cursor.fetchall()
            resultados_comparacao.append((vaca[1], dict(resultados)))
        
        conn.close()
        
        # Criar janela de comparação
        janela_comparacao = tk.Toplevel(self.root)
        janela_comparacao.title("Comparação de Vacas")
        janela_comparacao.geometry("800x600")
        
        # Criar notebook para abas
        notebook = ttk.Notebook(janela_comparacao)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Aba 1: Tabela comparativa
        aba_tabela = ttk.Frame(notebook)
        notebook.add(aba_tabela, text="Tabela Comparativa")
        
        # Criar tabela comparativa
        colunas = ['Nutriente'] + [vaca[1] for vaca in vacas_selecionadas]
        tree_comparacao = ttk.Treeview(aba_tabela, columns=colunas, show='headings')
        
        for coluna in colunas:
            tree_comparacao.heading(coluna, text=coluna)
            tree_comparacao.column(coluna, width=150)
        
        # Adicionar dados
        nutrientes = set()
        for _, resultados in resultados_comparacao:
            nutrientes.update(resultados.keys())
        
        for nutriente in sorted(nutrientes):
            valores = [nutriente]
            for _, resultados in resultados_comparacao:
                valores.append(str(resultados.get(nutriente, '-')))
            tree_comparacao.insert('', tk.END, values=valores)
        
        tree_comparacao.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Aba 2: Gráfico comparativo
        aba_grafico = ttk.Frame(notebook)
        notebook.add(aba_grafico, text="Gráfico Comparativo")
        
        # Criar gráfico comparativo
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Selecionar nutrientes principais para comparação
        nutrientes_principais = [
            "Energia (NEL, Mcal/dia)",
            "Proteína Bruta (PB, g/dia)",
            "Matéria Seca (MS, kg/dia)",
            "Cálcio (g/dia)",
            "Fósforo (g/dia)"
        ]
        
        x = np.arange(len(nutrientes_principais))
        largura = 0.15
        
        for i, (nome, resultados) in enumerate(resultados_comparacao):
            valores = [resultados.get(nutriente, 0) for nutriente in nutrientes_principais]
            ax.bar(x + i * largura, valores, largura, label=nome)
        
        ax.set_ylabel('Exigência Diária')
        ax.set_title('Comparação de Exigências Nutricionais')
        ax.set_xticks(x + largura * (len(resultados_comparacao) - 1) / 2)
        ax.set_xticklabels(nutrientes_principais, rotation=45, ha='right')
        ax.legend()
        
        canvas = FigureCanvasTkAgg(fig, master=aba_grafico)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def salvar_resultados_bd(self, nome, dados_vaca, resultados, id_vaca=None):
        # Se não foi fornecido ID, inserir a vaca primeiro
        if id_vaca is None:
            conn = sqlite3.connect('exigencias_vacas.db')
            cursor = conn.cursor()
            
            # Inserir vaca
            cursor.execute('''
            INSERT INTO vacas (nome, peso_vivo, producao_leite, gordura_leite, proteina_leite, dias_lactacao, 
                              num_lactacoes, peso_parto, dias_gestacao, raca, temperatura, umidade, manejo, data_cadastro)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                nome, dados_vaca['peso_vivo'], dados_vaca['producao_leite'], 
                dados_vaca['gordura_leite'], dados_vaca['proteina_leite'], 
                dados_vaca['dias_lactacao'], dados_vaca['num_lactacoes'],
                dados_vaca['peso_parto'], dados_vaca['dias_gestacao'],
                dados_vaca['raca'], dados_vaca['temperatura'], 
                dados_vaca['umidade'], dados_vaca['manejo'],
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ))
            
            id_vaca = cursor.lastrowid
            conn.commit()
            conn.close()
        
        # Conectar ao banco de dados
        conn = sqlite3.connect('exigencias_vacas.db')
        cursor = conn.cursor()
        
        # Inserir resultados
        data_calculo = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        for nutriente, valor in resultados.items():
            cursor.execute('''
            INSERT INTO resultados (id_vaca, nutriente, valor, data_calculo)
            VALUES (?, ?, ?, ?)
            ''', (id_vaca, nutriente, valor, data_calculo))
        
        conn.commit()
        conn.close()
        
        return id_vaca
    
    def atualizar_historico(self):
        # Limpar treeview
        for item in self.tree_historico.get_children():
            self.tree_historico.delete(item)
        
        # Conectar ao banco de dados
        conn = sqlite3.connect('exigencias_vacas.db')
        cursor = conn.cursor()
        
        # Selecionar resultados com nome da vaca
        cursor.execute('''
        SELECT r.id, v.nome, r.nutriente, r.valor, r.data_calculo
        FROM resultados r
        JOIN vacas v ON r.id_vaca = v.id
        ORDER BY r.data_calculo DESC
        ''')
        
        historico = cursor.fetchall()
        conn.close()
        
        # Adicionar ao treeview
        for item in historico:
            self.tree_historico.insert('', tk.END, values=item)
    
    def limpar_historico(self):
        if messagebox.askyesno("Confirmar", "Deseja realmente limpar todo o histórico?"):
            # Conectar ao banco de dados
            conn = sqlite3.connect('exigencias_vacas.db')
            cursor = conn.cursor()
            
            # Limpar tabela de resultados
            cursor.execute('DELETE FROM resultados')
            
            conn.commit()
            conn.close()
            
            # Atualizar histórico
            self.atualizar_historico()
            messagebox.showinfo("Sucesso", "Histórico limpo com sucesso!")

# Executar a aplicação
if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()