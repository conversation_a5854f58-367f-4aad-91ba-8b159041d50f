import tkinter as tk
from tkinter import ttk, messagebox
import math

def calcular_exigencias_nrc_expandido(peso_vivo, producao_leite, gordura_leite, proteina_leite, 
                                     dias_lactacao, num_lactacoes, peso_parto, dias_gestacao,
                                     raca, temperatura, umidade, manejo):
    """
    Calcula exigências nutricionais expandidas para vacas leiteiras (NRC 2001 e outros).
    Considera raça, condições ambientais e manejo.
    """
    # Fatores de ajuste
    fator_raca = {"Holandesa": 1.0, "Jersey": 1.1, "Gir": 1.05, "Girolando": 1.02}
    fator_raca_nel_pm = fator_raca.get(raca, 1.0)
    
    # Índice de Temperatura e Umidade (ITH)
    ith = (0.72 * (temperatura + umidade) + 40.6)
    fator_estresse_calor = 1.0
    if ith > 72:
        # Aumentar NEL em 5% para cada 10 unidades acima de 72
        fator_estresse_calor_nel = 1.0 + (0.05 * ((ith - 72) / 10))
        # Aumentar K em 1% e Na em 0.5% para cada 10 unidades acima de 72
        fator_estresse_calor_k = 1.0 + (0.01 * ((ith - 72) / 10))
        fator_estresse_calor_na = 1.0 + (0.005 * ((ith - 72) / 10))
    else:
        fator_estresse_calor_nel = 1.0
        fator_estresse_calor_k = 1.0
        fator_estresse_calor_na = 1.0
    
    # Fator de manejo (atividade)
    fator_manejo = {"Confinamento": 1.0, "Pasto": 1.1, "Semi-confinamento": 1.05}
    fator_atividade = fator_manejo.get(manejo, 1.0)
    
    # --- Cálculo de NEL (Energia Líquida de Lactação) ---
    NEL_mant = 0.08 * (peso_vivo ** 0.75) * fator_atividade
    NEL_lact = producao_leite * (0.0929 * gordura_leite + 0.0547 * proteina_leite + 0.192)
    NEL_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        t = dias_gestacao - 190
        NEL_gest = 0.00318 * peso_parto * math.exp(0.018 * t)
    NEL_total = (NEL_mant + NEL_lact + NEL_gest) * fator_raca_nel_pm * fator_estresse_calor_nel
    
    # --- Cálculo de PM (Proteína Metabolizável) ---
    PM_mant = 3.8 * ((peso_vivo / 1000) ** 0.75)
    PM_lact = producao_leite * (proteina_leite * 10) * 1.25
    PM_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        PM_gest = 1.136 * ((peso_parto / 1000) ** 0.7) * math.exp(-0.00203 * (dias_gestacao - 190))
    PM_total = (PM_mant + PM_lact + PM_gest) * fator_raca_nel_pm
    
    # --- Minerais (Cálcio e Fósforo) ---
    Ca_mant = peso_vivo * 0.0154
    Ca_lact = producao_leite * 1.22
    Ca_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        Ca_gest = 0.0076 * peso_parto * math.exp(0.014 * (dias_gestacao - 190))
    Ca_total = Ca_mant + Ca_lact + Ca_gest
    
    P_mant = peso_vivo * 0.014
    P_lact = producao_leite * 0.9
    P_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        P_gest = 0.0047 * peso_parto * math.exp(0.011 * (dias_gestacao - 190))
    P_total = P_mant + P_lact + P_gest
    
    # --- Vitaminas ---
    VitA_mant = 110 * peso_vivo
    VitA_lact = 30 * producao_leite
    VitA_gest = 0
    if dias_gestacao and dias_gestacao > 190:
        # Peso fetal estimado: 0.028 * (dias_gestacao - 190) ** 2
        peso_fetal = 0.028 * ((dias_gestacao - 190) ** 2)
        VitA_gest = 40 * peso_fetal
    VitA_total = VitA_mant + VitA_lact + VitA_gest
    
    VitD_mant = 30 * peso_vivo
    VitD_lact = 15 * producao_leite
    VitD_total = VitD_mant + VitD_lact
    
    VitE_mant = 1.5 * peso_vivo
    VitE_lact = 0.5 * producao_leite
    VitE_total = VitE_mant + VitE_lact
    
    # --- Outros Minerais ---
    Mg_mant = 0.004 * peso_vivo
    Mg_lact = 0.12 * producao_leite
    Mg_total = Mg_mant + Mg_lact
    
    K_mant = 0.039 * peso_vivo
    K_lact = 1.5 * producao_leite
    K_total = (K_mant + K_lact) * fator_estresse_calor_k
    
    Na_mant = 0.015 * peso_vivo
    Na_lact = 1.0 * producao_leite
    Na_total = (Na_mant + Na_lact) * fator_estresse_calor_na
    
    Cl_mant = 0.022 * peso_vivo
    Cl_lact = 1.2 * producao_leite
    Cl_total = Cl_mant + Cl_lact
    
    S_mant = 0.0006 * peso_vivo
    S_lact = 0.3 * producao_leite
    S_total = S_mant + S_lact
    
    Cu_mant = 0.1 * peso_vivo
    Cu_lact = 0.5 * producao_leite
    Cu_total = Cu_mant + Cu_lact
    
    Zn_mant = 0.5 * peso_vivo
    Zn_lact = 15 * producao_leite
    Zn_total = Zn_mant + Zn_lact
    
    Se_mant = 0.0006 * peso_vivo
    Se_lact = 0.05 * producao_leite
    Se_total = Se_mant + Se_lact
    
    I_mant = 0.006 * peso_vivo
    I_lact = 0.1 * producao_leite
    I_total = I_mant + I_lact
    
    Co_mant = 0.0001 * peso_vivo
    Co_lact = 0.01 * producao_leite
    Co_total = Co_mant + Co_lact
    
    Mn_mant = 0.002 * peso_vivo
    Mn_lact = 0.2 * producao_leite
    Mn_total = Mn_mant + Mn_lact
    
    # --- FDN e CNF ---
    # FDN mínimo: 1.2% do PV, mas pode ser ajustado pela produção
    FDN_min = 0.012 * peso_vivo  # kg/dia
    # CNF: não há exigência, mas pode ser estimado como 35-40% da MS
    
    return {
        "Energia (NEL, Mcal/dia)": round(NEL_total, 2),
        "Proteína Metabolizável (PM, g/dia)": round(PM_total, 0),
        "Cálcio (g/dia)": round(Ca_total, 1),
        "Fósforo (g/dia)": round(P_total, 1),
        "Magnésio (g/dia)": round(Mg_total, 1),
        "Potássio (g/dia)": round(K_total, 1),
        "Sódio (g/dia)": round(Na_total, 1),
        "Cloro (g/dia)": round(Cl_total, 1),
        "Enxofre (g/dia)": round(S_total, 1),
        "Cobre (mg/dia)": round(Cu_total, 0),
        "Zinco (mg/dia)": round(Zn_total, 0),
        "Selênio (mg/dia)": round(Se_total, 2),
        "Iodo (mg/dia)": round(I_total, 2),
        "Cobalto (mg/dia)": round(Co_total, 3),
        "Manganês (mg/dia)": round(Mn_total, 0),
        "Vitamina A (UI/dia)": round(VitA_total, 0),
        "Vitamina D (UI/dia)": round(VitD_total, 0),
        "Vitamina E (UI/dia)": round(VitE_total, 0),
        "FDN mínimo (kg/dia)": round(FDN_min, 1)
    }

# --- Interface Gráfica ---
class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Calculadora de Exigências Nutricionais - NRC 2001")
        self.root.geometry("600x700")
        
        # Frame principal
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Labels e Entradas
        labels = [
            ("Peso Vivo (kg):", 0), ("Produção de Leite (kg/dia):", 1),
            ("Gordura no Leite (%):", 2), ("Proteína no Leite (%):", 3),
            ("Dias de Lactação:", 4), ("Número de Lactações:", 5),
            ("Peso ao Parto (kg):", 6), ("Dias de Gestação (opcional):", 7),
            ("Temperatura (°C):", 8), ("Umidade (%):", 9)
        ]
        
        self.entries = {}
        for i, (text, row) in enumerate(labels):
            ttk.Label(main_frame, text=text).grid(row=row, column=0, sticky=tk.W, pady=2)
            entry = ttk.Entry(main_frame, width=15)
            entry.grid(row=row, column=1, sticky=tk.W, pady=2)
            self.entries[text] = entry
        
        # Raça
        ttk.Label(main_frame, text="Raça:").grid(row=10, column=0, sticky=tk.W, pady=2)
        self.raca_var = tk.StringVar()
        raca_combo = ttk.Combobox(main_frame, textvariable=self.raca_var, width=12)
        raca_combo['values'] = ('Holandesa', 'Jersey', 'Gir', 'Girolando')
        raca_combo.grid(row=10, column=1, sticky=tk.W, pady=2)
        raca_combo.current(0)
        
        # Manejo
        ttk.Label(main_frame, text="Manejo:").grid(row=11, column=0, sticky=tk.W, pady=2)
        self.manejo_var = tk.StringVar()
        manejo_combo = ttk.Combobox(main_frame, textvariable=self.manejo_var, width=12)
        manejo_combo['values'] = ('Confinamento', 'Pasto', 'Semi-confinamento')
        manejo_combo.grid(row=11, column=1, sticky=tk.W, pady=2)
        manejo_combo.current(0)
        
        # Botão Calcular
        calcular_btn = ttk.Button(main_frame, text="Calcular", command=self.calcular)
        calcular_btn.grid(row=12, column=0, columnspan=2, pady=10)
        
        # Área de Resultados
        self.result_text = tk.Text(main_frame, width=70, height=20)
        self.result_text.grid(row=13, column=0, columnspan=2, pady=10)
        
    def calcular(self):
        try:
            # Obter valores das entradas
            peso_vivo = float(self.entries["Peso Vivo (kg):"].get())
            producao_leite = float(self.entries["Produção de Leite (kg/dia):"].get())
            gordura_leite = float(self.entries["Gordura no Leite (%):"].get())
            proteina_leite = float(self.entries["Proteína no Leite (%):"].get())
            dias_lactacao = int(self.entries["Dias de Lactação:"].get())
            num_lactacoes = int(self.entries["Número de Lactações:"].get())
            peso_parto = float(self.entries["Peso ao Parto (kg):"].get())
            dias_gestacao_str = self.entries["Dias de Gestação (opcional):"].get()
            dias_gestacao = int(dias_gestacao_str) if dias_gestacao_str else None
            temperatura = float(self.entries["Temperatura (°C):"].get())
            umidade = float(self.entries["Umidade (%):"].get())
            raca = self.raca_var.get()
            manejo = self.manejo_var.get()
            
            # Calcular exigências
            resultados = calcular_exigencias_nrc_expandido(
                peso_vivo, producao_leite, gordura_leite, proteina_leite,
                dias_lactacao, num_lactacoes, peso_parto, dias_gestacao,
                raca, temperatura, umidade, manejo
            )
            
            # Exibir resultados
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "Exigências Nutricionais Diárias:\n\n")
            for nutriente, valor in resultados.items():
                self.result_text.insert(tk.END, f"{nutriente}: {valor}\n")
                
        except ValueError:
            messagebox.showerror("Erro", "Por favor, preencha todos os campos com valores numéricos válidos.")

if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()